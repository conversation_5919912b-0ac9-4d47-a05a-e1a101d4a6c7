<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.0"?>
<!--
    Copyright (c) 2005, 2010 IBM Corporation and others.
    All rights reserved. This program and the accompanying materials
    are made available under the terms of the Eclipse Public License v1.0
    which accompanies this distribution, and is available at
    http://www.eclipse.org/legal/epl-v10.html
   
    Contributors:
         IBM Corporation - initial API and implementation
 -->

<plugin>

    
<!-- ========== Extension Points ================= -->
<!-- =============================================================================== -->
<!-- Extension point: org.eclipse.ui.intro.config                                    -->
<!-- Extension-point for contributing a configuration to a Customizable Intro Part.  -->
<!--                                                                                 -->
<!-- =============================================================================== -->
<!-- ================================================================================= -->
<!-- Extension point: org.eclipse.ui.intro.configExtension                             -->
<!-- Extension-point for contributing an extension to an existing intro configuration  -->
<!--                                                                                   -->
<!-- ================================================================================= -->
   
   <extension
         point="org.eclipse.ui.intro">
      <intro
            class="org.eclipse.ui.intro.config.CustomizableIntroPart"
            contentDetector="org.eclipse.ui.internal.intro.universal.contentdetect.ContentDetector"
            icon="$nl$/icons/welcome16.gif"
            id="org.eclipse.ui.intro.universal"/>
   </extension>
   <extension
         point="org.eclipse.ui.intro.config">
      <config
            configurer="org.eclipse.ui.internal.intro.universal.UniversalIntroConfigurer"
            content="$nl$/introContent.xml"
            id="org.eclipse.ui.intro.universalConfig"
            introId="org.eclipse.ui.intro.universal">
         <presentation
               home-page-id="root" standby-page-id="standby">
            <implementation
                  style="themes/shared/html/shared.css,$theme$/html/shared.css,$theme$/html/font-$fontStyle$.css,$theme$/html/$direction$.css"
                  kind="html"
                  os="win32,linux,macosx,solaris">
            </implementation>
            <implementation
                  kind="swt">
            </implementation>
            <launchBar
               	location="fastview" 
            	bg="$launchbarBackground$"
            	computed="true">
            </launchBar>
         </presentation>
      </config>
   </extension>
   <extension
         point="org.eclipse.ui.intro.configExtension">
      <theme
            id="org.eclipse.ui.intro.universal.circles"
            name="%theme.name.circles"
            path="$nl$/themes/circles"
            previewImage="$nl$/themes/circles/preview.png"
            scalable="true">
            <property name="workbenchAsRootLink"
            		value="true"/>
            		<!--
            <property name="launchbarBackground"
            		value="#a1c2cb"/>
            		-->
            <property name="launchbarOverviewIcon"
            		  value="$theme$graphics/launchbar/overview16.png"/>
            <property name="launchbarFirststepsIcon"
            		  value="$theme$graphics/launchbar/firststeps16.png"/>
            <property name="launchbarTutorialsIcon"
            		  value="$theme$graphics/launchbar/tutorials16.png"/>
            <property name="launchbarSamplesIcon"
            		  value="$theme$graphics/launchbar/samples16.png"/>
            <property name="launchbarWhatsnewIcon"
            		  value="$theme$graphics/launchbar/whatsnew16.png"/>
            <property name="launchbarMigrateIcon"
            		  value="$theme$graphics/launchbar/migrate16.png"/>
            <property name="launchbarWebresourcesIcon"
            		  value="$theme$graphics/launchbar/webresources16.png"/>

            <property name="highContrast-overview"
            		  value="$theme$graphics/icons/ctool/overview.png"/>
            <property name="highContrast-firststeps"
            		  value="$theme$graphics/icons/ctool/firststeps.png"/>
            <property name="highContrast-tutorials"
            		  value="$theme$graphics/icons/ctool/tutorials.png"/>
            <property name="highContrast-samples"
            		  value="$theme$graphics/icons/ctool/samples.png"/>
            <property name="highContrast-whatsnew"
            		  value="$theme$graphics/icons/ctool/whatsnew.png"/>
            <property name="highContrast-webresources"
            		  value="$theme$graphics/icons/ctool/webresources.png"/>
            <property name="highContrast-migrate"
            		  value="$theme$graphics/icons/ctool/migrate.png"/>    
            <property name="highContrast-workbench"
            		  value="$theme$graphics/icons/ctool/workbench.png"/>    
            		  
            <property name="highContrastNav-overview"
            		  value="$theme$graphics/icons/ctool/ov_nav.png"/>
            <property name="highContrastNav-firststeps"
            		  value="$theme$graphics/icons/ctool/fs_nav.png"/>
            <property name="highContrastNav-tutorials"
            		  value="$theme$graphics/icons/ctool/tu_nav.png"/>
            <property name="highContrastNav-samples"
            		  value="$theme$graphics/icons/ctool/sa_nav.png"/>
            <property name="highContrastNav-whatsnew"
            		  value="$theme$graphics/icons/ctool/wn_nav.png"/>
            <property name="highContrastNav-webresources"
            		  value="$theme$graphics/icons/ctool/wr_nav.png"/>
            <property name="highContrastNav-migrate"
            		  value="$theme$graphics/icons/ctool/mi_nav.png"/> 
            <property name="highContrastNav-workbench"
            		  value="$theme$graphics/icons/ctool/wb_nav.png"/>
      </theme>
      <theme
            id="org.eclipse.ui.intro.universal.purpleMesh"
            name="%theme.name.purpleMesh"
            path="$nl$/themes/purpleMesh"
            previewImage="$nl$/themes/purpleMesh/preview.png"
            scalable="true">
            <property name="launchbarBackground"
            		value="#c6c3e8"/>
            <property name="capitalizeTitles"
            		value="true"/>
            <property name="launchbarOverviewIcon"
            		  value="$theme$graphics/launchbar/overview.gif"/>
            <property name="launchbarFirststepsIcon"
            		  value="$theme$graphics/launchbar/firststeps16.png"/>
            <property name="launchbarTutorialsIcon"
            		  value="$theme$graphics/launchbar/tutorials.gif"/>
            <property name="launchbarSamplesIcon"
            		  value="$theme$graphics/launchbar/samples.gif"/>
            <property name="launchbarWhatsnewIcon"
            		  value="$theme$graphics/launchbar/whatsnew.gif"/>
            <property name="launchbarMigrateIcon"
            		  value="$theme$graphics/launchbar/migrate16.png"/>
            <property name="launchbarWebresourcesIcon"
            		  value="$theme$graphics/launchbar/webresources16.png"/>
            		  
            <property name="highContrast-overview"
            		  value="$theme$graphics/icons/etool/overview72.gif"/>
            <property name="highContrast-firststeps"
            		  value="$theme$graphics/icons/etool/firsteps72.gif"/>
            <property name="highContrast-tutorials"
            		  value="$theme$graphics/icons/etool/tutorials72.gif"/>
            <property name="highContrast-samples"
            		  value="$theme$graphics/icons/etool/samples72.gif"/>
            <property name="highContrast-whatsnew"
            		  value="$theme$graphics/icons/etool/whatsnew72.gif"/>
            <property name="highContrast-webresources"
            		  value="$theme$graphics/icons/etool/webrsrc72.gif"/>
            <property name="highContrast-migrate"
            		  value="$theme$graphics/icons/etool/migrate72.gif"/>
            <property name="highContrast-workbench"
            		  value="$theme$graphics/icons/etool/wb48.gif"/> 
            		     
            <property name="highContrastNav-overview"
            		  value="$theme$graphics/icons/etool/overview48.gif"/>
            <property name="highContrastNav-firststeps"
            		  value="$theme$graphics/icons/etool/firsteps48.gif"/>
            <property name="highContrastNav-tutorials"
            		  value="$theme$graphics/icons/etool/tutorials48.gif"/>
            <property name="highContrastNav-samples"
            		  value="$theme$graphics/icons/etool/samples48.gif"/>
            <property name="highContrastNav-whatsnew"
            		  value="$theme$graphics/icons/etool/whatsnew48.gif"/>
            <property name="highContrastNav-webresources"
            		  value="$theme$graphics/icons/etool/webrsrc48.gif"/>
            <property name="highContrastNav-migrate"
            		  value="$theme$graphics/icons/etool/migrate48.gif"/> 
            <property name="highContrastNav-workbench"
            		  value="$theme$graphics/icons/etool/wb48.gif"/>       
      </theme> 
      <theme
            default="true"
            id="org.eclipse.ui.intro.universal.slate"
            name="%theme.name.slate"
            path="$nl$/themes/slate"
            previewImage="$nl$/themes/slate/preview.png"
            scalable="true">
            <property name="workbenchAsRootLink"
            		value="true"/>
            		<!--
            <property name="launchbarBackground"
            		value="#a1c2cb"/>
            		-->
            <property name="launchbarOverviewIcon"
            		  value="$theme$graphics/launchbar/overview16.png"/>
            <property name="launchbarFirststepsIcon"
            		  value="$theme$graphics/launchbar/firststeps16.png"/>
            <property name="launchbarTutorialsIcon"
            		  value="$theme$graphics/launchbar/tutorials16.png"/>
            <property name="launchbarSamplesIcon"
            		  value="$theme$graphics/launchbar/samples16.png"/>
            <property name="launchbarWhatsnewIcon"
            		  value="$theme$graphics/launchbar/whatsnew16.png"/>
            <property name="launchbarMigrateIcon"
            		  value="$theme$graphics/launchbar/migrate16.png"/>
            <property name="launchbarWebresourcesIcon"
            		  value="$theme$graphics/launchbar/webresources16.png"/>

            <property name="highContrast-overview"
            		  value="$theme$graphics/icons/ctool/overview.png"/>
            <property name="highContrast-firststeps"
            		  value="$theme$graphics/icons/ctool/firststeps.png"/>
            <property name="highContrast-tutorials"
            		  value="$theme$graphics/icons/ctool/tutorials.png"/>
            <property name="highContrast-samples"
            		  value="$theme$graphics/icons/ctool/samples.png"/>
            <property name="highContrast-whatsnew"
            		  value="$theme$graphics/icons/ctool/whatsnew.png"/>
            <property name="highContrast-webresources"
            		  value="$theme$graphics/icons/ctool/webresources.png"/>
            <property name="highContrast-migrate"
            		  value="$theme$graphics/icons/ctool/migrate.png"/>    
            <property name="highContrast-workbench"
            		  value="$theme$graphics/icons/ctool/workbench.png"/>    
            		  
            <property name="highContrastNav-overview"
            		  value="$theme$graphics/icons/ctool/ov_nav.png"/>
            <property name="highContrastNav-firststeps"
            		  value="$theme$graphics/icons/ctool/fs_nav.png"/>
            <property name="highContrastNav-tutorials"
            		  value="$theme$graphics/icons/ctool/tu_nav.png"/>
            <property name="highContrastNav-samples"
            		  value="$theme$graphics/icons/ctool/sa_nav.png"/>
            <property name="highContrastNav-whatsnew"
            		  value="$theme$graphics/icons/ctool/wn_nav.png"/>
            <property name="highContrastNav-webresources"
            		  value="$theme$graphics/icons/ctool/wr_nav.png"/>
            <property name="highContrastNav-migrate"
            		  value="$theme$graphics/icons/ctool/mi_nav.png"/> 
            <property name="highContrastNav-workbench"
            		  value="$theme$graphics/icons/ctool/wb_nav.png"/>
      </theme>
   </extension>
</plugin>
