/*******************************************************************************
 * Copyright (c) 2006 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials 
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * 
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *******************************************************************************/

/* show the "selected" image for this page */
#navigation-links a#tutorials img, 
#navigation-links a#tutorials:hover img,
#navigation-links a#tutorials:focus img,
#navigation-links a#tutorials:active img {
	background-image : url(../graphics/icons/ctool/tu_nav_64.gif); 
	top : 2px; 
	left : -3px;
}

#navigation-links {
	background-image: url(../graphics/contentpage/tu_banner.jpg);
}

#navigation-links a:hover#tutorials .link-label,
#navigation-links a:focus#tutorials .link-label,
#navigation-links a:active#tutorials .link-label {
	display : none;
}

#navigation-links a:hover#tutorials,
#navigation-links a:focus#tutorials,
#navigation-links a:active#tutorials {
	background-image : none;
}

#navigation-links a:hover#tutorials .link-extra-div,
#navigation-links a:focus#tutorials .link-extra-div,
#navigation-links a:active#tutorials .link-extra-div {
	background-image : none;
}