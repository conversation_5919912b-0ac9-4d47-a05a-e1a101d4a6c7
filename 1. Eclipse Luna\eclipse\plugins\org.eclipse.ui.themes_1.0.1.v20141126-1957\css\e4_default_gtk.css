/*******************************************************************************
 * Copyright (c) 2010, 2014 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *     <PERSON> <<PERSON>.<EMAIL>> - Bug 420836
 *******************************************************************************/

@import url("platform:/plugin/org.eclipse.ui.themes/css/e4_basestyle.css");

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_START {
	color: #DCDCDC;
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_END {
	color: #E1E1E1;
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_TAB_OUTLINE_COLOR {
	color: #B4B4B4;
}

.MTrimmedWindow {
	background-color: #E2E2E2;
}

.MPartStack {
	swt-simple: false;
	swt-mru-visible: false;
}

.MTrimBar {
	background-color: #E2E2E2;
}

.MTrimBar#org-eclipse-ui-main-toolbar  {
	background-color: COLOR-WIDGET-BACKGROUND #E2E2E2 100%;
}

.MToolControl.TrimStack {
	frame-image:  url(./gtkTSFrame.png);
	handle-image:  url(./gtkHandle.png);
}

#PerspectiveSwitcher {
	background-color: COLOR-WIDGET-BACKGROUND #E2E2E2 100%;
	eclipse-perspective-keyline-color: #B4B4B4 #B4B4B4;
}

#org-eclipse-ui-editorss {
	swt-tab-height: 8px;
	padding: 0px 5px 7px;
}

CTabFolder.MArea .MPartStack, CTabFolder.MArea .MPartStack.active {
	swt-shadow-visible: false;
}

CTabFolder Canvas {
	background-color: #F8F8F8;
}

