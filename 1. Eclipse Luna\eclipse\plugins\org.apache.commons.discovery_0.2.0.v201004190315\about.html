<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1" />
<title>About</title>
</head>
<body lang="EN-US">
<h2>About This Content</h2>

<p>June 5, 2007</p>
<h3>License</h3>

<p>The Eclipse Foundation makes available all content in this
plug-in ("Content"). Unless otherwise indicated below, the Content is
provided to you under the terms and conditions of the Eclipse Public
License Version 1.0 ("EPL"). A copy of the EPL is available at <a
	href="http://www.eclipse.org/legal/epl-v10.html">http://www.eclipse.org/legal/epl-v10.html</a>.
For purposes of the EPL, "Program" will mean the Content.</p>

<p>If you did not receive this Content directly from the Eclipse
Foundation, the Content is being redistributed by another party
("Redistributor") and different terms and conditions may apply to your
use of any object code in the Content. Check the Redistributor's license
that was provided with the Content. If no such license exists, contact
the Redistributor. Unless otherwise indicated below, the terms and
conditions of the EPL still apply to any source code in the Content and
such source code may be obtained at <a href="http://www.eclipse.org/">http://www.eclipse.org</a>.
</p>

<h3>Third Party Content</h3>
<p>The Content includes items that have been sourced from third
parties as set out below. If you did not receive this Content directly
from the Eclipse Foundation, the following is provided for informational
purposes only, and you should look to the Redistributor's license for
terms and conditions of use.</p>

<h4>Apache Axis 1.4.0 (subset)</h4>

<p>The plug-in is accompanied by a subset of Apache Axis 1.3.0
(&quot;Axis&quot;) developed by the Apache Software Foundation as part
of the Apache Web Services project. This bundle does NOT include the following
files from the original Axis distribution:</p>
<ul>
	<li>axis-schema.jar</li>
	<li>commons-logging-1.0.4.jar</li>
	<li>log4j-1.2.8.jar</li>
	<li>axis.jar</li>
	<li>axis-ant.jar</li>
	<li>jaxrpc.jar</li>
	<li>saaj.jar</li>
</ul>

<p>but DOES include the following files which are located in the lib
directory of the plug-in:</p>

<ul>
	<li>commons-discovery-0.2.jar</li>
</ul>

<p>Your use of Apache Axis is subject to the terms and conditions of
the Apache License, Version 1.1 (&quot;Apache License&quot;). A copy of
the Apache License can be found in <a href="about_files/LICENSE.txt">about_files/LICENSE.txt</a>
and is also available at <a
	href="http://www.apache.org/licenses/LICENSE-1.1">http://www.apache.org/licenses/LICENSE-1.1</a>.</p>

<p>The source for the  bundle is available on
the Apache Axis website at <a
    href="http://ws.apache.org/axis/">http://ws.apache.org/axis/</a>.</p>

</body>
</html>