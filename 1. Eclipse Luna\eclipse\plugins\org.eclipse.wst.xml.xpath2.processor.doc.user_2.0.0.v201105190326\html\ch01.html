<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Introduction</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="index.html" title="usermanual"><link rel="prev" href="index.html" title="usermanual"><link rel="next" href="ch02.html" title="How to feed Psychopath XPath expressions"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="chapter" title="Introduction"><div class="titlepage"><div><div><h2 class="title"><a name="Introduction"></a>Introduction</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><ul><li><span class="section"><a href="ch01.html#Getting_PsychoPath">Getting PsychoPath</a></span></li></ul></div><p>What is PsychohPath?   PsychoPath is a XPath 2.0 XML Schema Aware processor.  It is nearly fully compliant to the XPath 2.0 test suite.  It is a library that does not require eclipse to be used.   Known adopters of PsychoPath include the Xerces-J project for XML Schemas 1.1 assertion support.</p><p>PsychoPath is the only known open-source java XPath 2.0 processor that is fully schema aware.   SAXON HE only supports the core functionality.   XML Schema awarness provides tighter static checking, and can be used to help determine if certain operations can or should occur on an XML node.   For a detailed description of the PsychoPath's design please see the 
			<a class="ulink" href="/wiki/PsychoPathXPathProcessor/Design" target="_top">design</a> document.
		</p><div class="section" title="Getting PsychoPath"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Getting_PsychoPath"></a>Getting PsychoPath</h2></div></div></div><p>Standalone jars are only available through nightly builds.  However, you can download the current WTP builds, and use the org.eclipse.wst.xml.xpath2.processor.jar file.   This jar has no dependencies on eclipse, and will work as a standard jar file.  If you are using an OSGI container, then this is treated as a standard OSGI bundle.</p><p>Additional dependencies you currently need are:</p><div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem"><p>IBM ICU 3.8</p></li><li class="listitem"><p>Xerces 2.8.0 or greater</p></li><li class="listitem"><p>JavaCup 0.10 or greater.</p></li></ul></div><p>These are all available from the 
				<a class="ulink" href="http://download.eclipse.org/tools/orbit/downloads/" target="_top">Orbit project.</a>
			</p></div></div></body></html>