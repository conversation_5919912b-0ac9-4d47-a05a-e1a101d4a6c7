/*******************************************************************************
 * Copyright (c) 2006, 2010 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials 
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * 
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *******************************************************************************/

/* 
 * Set up general fonts, sizes and colors 
 */
body { font-family : Arial, sans-serif; }

H1, H2, H3, H4, p, a { color : #4D4D4D; }

 body {
	background-color : #FFFFFF;
}

/* The label part of the folding section */
.section-title-link .section-title {
	display : inline;
}

h2 {
	font-weight : normal;
	color : #7B8694;
}

/* For regular div labels */
H4 .div-label {
	font-family: Verdana, Arial, Helvetica; 
	font-weight: bold; 
	color: #4A4D4A; 
	line-height:1.3;
}

/* For the main page content's title */
#content-header H4 .div-label {
	font-family: Verdana, Arial, Helvetica;
	color:#333333; 
	font-weight: normal; 
	letter-spacing:-0.03em;
	margin-left: 68px;
	float : none;
	clear : both;
}

/* For separators */
HR {
	width: 90%;
	align: left;
	height : 1px;
	color :  #dfdfe4;
}

/* Page description if the page has it. */
.page-description {
	display: block;
	font-family: Verdana, Arial, Helvetica; 
	line-height:1.3;
	float : none;
	clear : both;
	margin-left: 70px;
	position : relative;
	top : -25px;
}

a {
	font-weight : bold;
	text-decoration : none;
	color : #4D4D4D;
}

a:hover  {
	color: #69c; 
}

/* General link labels */
a .link-label {
	font-weight : normal;
}

/* Floating link labels for navigation links */
#navigation-links a .link-label,
#action links a .text {
	font-weight : bold;	
	color : white;
}

#navigation-links a#overview:hover .link-label
{
    color : #FFEC89;
}

#navigation-links a#tutorials:hover .link-label,
#navigation-links a#samples:hover .link-label
{
    color : #ACCCE9;
}

#navigation-links a#whatsnew:hover .link-label,
#navigation-links a#firststeps:hover .link-label
{
    color : #E5CD89;
}

#navigation-links a#webresources:hover .link-label ,
#navigation-links a#migrate:hover .link-label
{
    color : #BCD77C;
}

#action-links a:hover .link-label {
    color : E0DFE3;
}

/* Text in links. */
a .text {
	font-weight : normal;
}

p .group-description {
	font-family: Verdana, Arial, Helvetica; 
	font-weight : normal;
}

/* Hide the extra div in links by default. */
.link-extra-div {
	display : none;
}

/* 
 * Set up other general properties like padding/margins
 */
html, body { width : 100%; height : 100%; }

html, body, div, h1, h4, p, a { margin : 0px; padding : 0px; }

/* 
 * Page header - adding extra padding at the bottom to compensate
 * for navigation background/header overlap.
 */
#page-content #content-header {
    padding-top : 10px;
	padding-bottom : 22px;
}

/* For regular div labels */
#page-content div H4 {
	padding : 10px;
	padding-bottom : 0px;
}

/* For the main page content's div label */
#page-content #content-header H4 {
	padding-bottom : 10px;
	padding-top : 0px;
}

/* special case for Mozilla's main content-header label.
   Mozilla 1.4 needs more room at the top */
#page-content > #content-header H4 { padding-top : 10px; }

/* Needed in IE to get shift+tab to show the active image properly */
a:active {
	border : solid 0px;
}

a img {
	border-width : 0;
	background-repeat : no-repeat;
}

/*
 * to get scrollbars working in both IE and Mozilla
 */

/*
 * to get scrollbars working in both IE and Mozilla
 */
html,body { overflow: auto; }
html>body { overflow: visible; }

/*
 * Set up the body, decorative background, and navigation for the content 
 * pages. 
 * Note: the root page handles its own background and navigation; these
 * settings primarily apply to the content pages
 */
body {
    background-image: url(../graphics/contentpage/banner_extension.jpg);	
    background-repeat: repeat-x;
    background-position: top left;
}

/*
 * Hide the general-purpose groups - not using them in this theme.
 */
#extra-group1,
#extra-group2,
#extra-group3,
#extra-group4,
#extra-group5 {
	display : none;
}

/*
 * Dimensions.
 */
body, .page {
	/* since IE doesn't support min-width, try expression */
	height : 100%;
}

.page {
	background-repeat : no-repeat;
	background-position : top left;
	
	min-width : 770px;
	width:expression(document.body.clientWidth < 770? "770px": "auto" );
    min-height : 425px;
	height : expression(document.body.clientHeight < 425? "425px": "100%" );
}

#page-content {
	background-repeat : no-repeat;
	background-position : bottom right;
	height : 65%;
}

/* 
 * Lay out the navigation links 
 * (Root page does something similar for its navigation)
 */
#navigation-links {
	position : relative;
	left : 0px;
	top : 0px;
	padding-left: 12px;
	height : 118px;
}

#navigation-links a {
	text-align : left;
	height : 64px;
	float : left;
	margin-left : 10px;
	position : relative;
	text-align : center;
}

#action-links a {
    float : right;
    margin-right : 20px;
}

#navigation-links a img {
	height : 32px;
	width : 32px;
	vertical-align : center;
	horizontal-align : center; 
	display : block;
	margin-top : 5px;
	margin-bottom : 0px;
	margin-left : auto;
	margin-right : auto;
}

#navigation-links a.high-contrast img {
	margin-bottom : 0px;
}


/*
 * Not showing description for navigation links.
 */
#navigation-links a .text { display : none; }

/* properties for each of the navigation-links  */
#navigation-links a#overview img { 
	background-image : url(../graphics/icons/ctool/ov_nav_32.gif); 
}

#navigation-links a#firststeps img { 
	background-image : url(../graphics/icons/ctool/fs_nav_32.gif); 
}

#navigation-links a#tutorials img { 
	background-image : url(../graphics/icons/ctool/tu_nav_32.gif); 
}

#navigation-links a#samples img { 
	background-image : url(../graphics/icons/ctool/sa_nav_32.gif); 
}

#navigation-links a#whatsnew img { 
	background-image : url(../graphics/icons/ctool/wn_nav_32.gif); 
}

#navigation-links a#migrate img { 
	background-image : url(../graphics/icons/ctool/mi_nav_32.gif); 
}

#navigation-links a#webresources img { 
	background-image : url(../graphics/icons/ctool/wr_nav_32.gif); 
}

#navigation-links a#workbench img { 
	background-image : url(../graphics/icons/ctool/wb_nav_32.gif); 
}

#action-links a {
	text-align : center;
}

#action-links a.high-contrast .link-label {
	display: none;
}

#navigation-links a.high-contrast .background-image {
	display: none;
}

#page-links a.high-contrast:focus .link-label,
#page-links a.high-contrast:active .link-label {
	display: block !important;
	text-decoration: underline;
	top : 5px;
}

#page-links span {
    display : block;
    margin-top : -2px;
}

/* 
 * Lay out the page title and description 
 */
h1, p { margin-left : 10px; } /* required in mozilla so the page description is properly indented */

/* position the page content so that the page title overlays the bottom
 * of the background image, but make sure the content is always on top 
 * (using z-index) */

#page-content {
	float : none;
	clear : both;
	text-align : center;
	position : relative;
	top : -50px;
	margin-bottom: -50px;
	z-index : 10;
}

#page-content p { 
	padding-bottom : 15px; 
	text-align : left; 
	float : none;
	clear : both;
}

/* Page content quadrants. Page content is placed in four quadrants.
 * Upper pair is separated from the bottom pair with a divider
 * to ensure bottom pair is aligned even with the uneven content
 * in the upper pair.
 */

#page-content #top-left {
  border: none; float: left; margin: 0px; padding: 0px; width: 49%;
  clear: left;
}
#page-content #top-right {
  border: none; float: right; margin: 0px; padding: 0px; width: 49%;
  clear: right;
}
/* top-bottom divider - runs the entire width to ensure
 * bottom boxes start at the same y
 */
#page-content #content-divider {
  border: none; float: none; margin: 0; padding: 0px; width: 100%;
  clear: both;
}

#page-content #bottom-left {
  border: none; float: left; margin: 0px; padding: 0px; width: 49%;
  clear: left;
}
#page-content #bottom-right {
  border: none; float: right; margin: 0px; padding: 0px; width: 49%;
  clear: right;
}

#page-content #content-header H4, .page-description {
	text-align : left;
	margin-right : 10px;
	float : none;
	clear : both;
}

#page-content * > a {
	vertical-align : middle; 
}

#page-content * a img {
	height : 57px;
	width : 57px;
	vertical-align : middle;
}	

#page-content * a .link-label {
	display : block;
	position : relative;
	top : -50px;
	left : 60px;
	margin-right: 60px;
}

#page-content * a > .link-label { left: 65px; }

#page-content * a p  {
	display : block;
	position : relative;
	top : -45px;
	margin-bottom: -25px;
}

#page-content * a p .text {
	display : block;
	position : relative;
	left : 53px;
	margin-right: 53px;
}

#page-content * a p > .text { left: 56px; }

#page-content * a:hover { border-right : 5px; }

/* The following rules are for extensions in all pages. Extensions should be placed in
 * groups with the style 'content-group' and contain links with the style 'content-link'.
 * Group is important so that importance mixin style can be applied to the group that
 * uses block display. We need to see a solid rectangle around the extension, not 
 * a tight polygon around the link perimeter.
 */

.content-group {
	margin-left: 10px;
	margin-right: 10px;
	padding-left: 10px;
	padding-right: 10px;
	float : none;
	clear : both;
	text-align: left;
}

.content-link .link-label {
	font-family: Verdana, Arial, Helvetica; 
	font-weight: bold; 
	line-height:1.5;
	color: #00507C;
}

.content-link:hover .link-label {
	color: #69c; 
	text-decoration : underline;
}

.content-link .text {
	font-family: Verdana, Arial, Helvetica; 
	line-height: 1.3;
}

.categoryContentnav {
	font-family: Verdana, Arial, Helvetica; 
	font-weight: bold; 
	color: #4A4D4A; 
	line-height:1.3;
}

.contentpgNavhover {
	font-family: Verdana, Arial, Helvetica; 
	font-weight: bold; 
	color: #000; 
}

.topicList {
	font-family: Verdana, Arial, Helvetica; 
	line-height:1.75;
	color: #00507C;
}

.topicList:hover {
	color: #69c;
}


.rss-feed-link a {
	font-family: Verdana, Arial, Helvetica; 
	color: #00507C;
}

/*
 * This part is for hosting embedded document inside
 * the content area.
 */

iframe {
	position:relative;
	top:16px;
	width:100%;
	height:100%;
	padding-left:10px;
}

/* mozilla scrollbar appearing off page fix */
#page-content > iframe {
	width: 98%;
	padding-left: 2%;
}