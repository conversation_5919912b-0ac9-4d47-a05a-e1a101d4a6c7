###############################################################################
# Copyright (c) 2003, 2011 IBM Corporation and others.
# All rights reserved. This program and the accompanying materials
# are made available under the terms of the Eclipse Public License v1.0
# which accompanies this distribution, and is available at
# http://www.eclipse.org/legal/epl-v10.html
#
# Contributors:
#     IBM Corporation - initial API and implementation
#     Compuware Corporation - <PERSON><PERSON><PERSON> Angers <<EMAIL>> 
#     	- Enabled additional mirror slicingOptions in Headless PDE Build
#     	- Enabled 'raw' attribute for mirror step in Headless PDE Build
#     	- https://bugs.eclipse.org/338878
###############################################################################
#####################
# Parameters describing how and where to execute the build.
# Typical users need only update the following properties:
#    baseLocation - where things you are building against are installed
#    bootclasspath - The base jars to compile against (typicaly rt.jar)
#    configs - the list of {os, ws, arch} configurations to build.  
#
# Of course any of the settings here can be overridden by spec'ing 
# them on the command line (e.g., -DbaseLocation=d:/eclipse

#The type of the top level element we are building,  generally "feature"
topLevelElementType = feature
#The id of the top level element we are building
topLevelElementId = org.foo.bar

############# PRODUCT/PACKAGING CONTROL #############
product=/plugin or feature id/path/to/.product
runPackager=true

#Set the name of the archive that will result from the product build.
#archiveNamePrefix=

# The prefix that will be used in the generated archive.
archivePrefix=eclipse

# The location underwhich all of the build output will be collected.
collectingFolder=${archivePrefix}

# The list of {os, ws, arch} configurations to build.  This 
# value is a '&' separated list of ',' separate triples.  For example, 
#     configs=win32,win32,x86 & linux,motif,x86
# By default the value is *,*,*
configs = *, *, *
#configs=win32, win32, x86 & \
#	win32,win32,x86_64 & \
#	win32,win32,wpf & \
#	linux, gtk, ppc & \
#	linux, gtk, x86 & \
#	linux, gtk, x86_64 & \
#	linux, motif, x86 & \
#	solaris, motif, sparc & \
#	solaris, gtk, sparc & \
#	aix, motif, ppc & \
#	hpux, motif, ia64_32 & \
#	macosx, carbon, ppc & \
#	macosx, carbon, x86 & \
#	macosx, cocoa, ppc & \
#	macosx, cocoa, x86 & \
#	macosx, cocoa, x86_64

# By default PDE creates one archive (result) per entry listed in the configs property.
# Setting this value to true will cause PDE to only create one output containing all 
# artifacts for all the platforms listed in the configs property.
# To control the output format for the group, add a "group, group, group - <format>" entry to the
# archivesFormat. 
#groupConfigurations=true

#The format of the archive. By default a zip is created using antZip.
#The list can only contain the configuration for which the desired format is different than zip.
#archivesFormat=win32, win32, x86 - antZip& \
#	linux, gtk, ppc - antZip &\
#    linux, gtk, x86 - antZip& \
#	linux, gtk, x86_64 - antZip& \
# linux, motif, x86 - antZip& \
#	solaris, motif, sparc - antZip& \
#	solaris, gtk, sparc - antZip& \
#	aix, motif, ppc - antZip& \
#	hpux, motif, PA_RISC - antZip& \
#	macosx, carbon, ppc - antZip

#Allow cycles involving at most one bundle that needs to be compiled with the rest being binary bundles.
allowBinaryCycles = true

#Sort bundles depenedencies across all features instead of just within a given feature.
#flattenDependencies = true

#Parallel compilation, requires flattenedDependencies=true
#parallelCompilation=true
#parallelThreadCount=
#parallelThreadsPerProcessor=
	
#Set to true if you want the output to be ready for an update jar (no site.xml generated)
#outputUpdateJars = false

#Set to true for Jnlp generation
#codebase should be a URL that will be used as the root of all relative URLs in the output.
#generateJnlp=false
#jnlp.codebase=<codebase url>
#jnlp.j2se=<j2se version>
#jnlp.locale=<a locale>
#jnlp.generateOfflineAllowed=true or false generate <offlineAllowed/> attribute in the generated features
#jnlp.configs=${configs}			#uncomment to filter the content of the generated jnlp files based on the configuration being built

#Set to true if you want to sign jars
#signJars=false
#sign.alias=<alias>
#sign.keystore=<keystore location>
#sign.storepass=<keystore password>
#sign.keypass=<key password>

#Arguments to send to the zip executable
zipargs=

#Arguments to send to the tar executable
tarargs=

#Control the creation of a file containing the version included in each configuration - on by default 
#generateVersionsLists=false

############ REPO MIRROR OPTIONS CONTROL ############
# Default values for the slicingOptions and raw attribute of the p2.mirror Ant target used to generate the p2 repo (buildRepo)
# Note that the default values used by PDE/Build are different from the default values for p2.mirror's slicingOptions and raw attribute
# See http://help.eclipse.org/topic//org.eclipse.platform.doc.isv/guide/p2_repositorytasks.htm for the details
# of each setting.
#p2.mirror.slicing.filter=
#p2.mirror.slicing.followOnlyFilteredRequirements=false
#p2.mirror.slicing.followStrict=false
#p2.mirror.slicing.includeFeatures=true
#p2.mirror.slicing.includeNonGreedy=false
#p2.mirror.slicing.includeOptional=true
#p2.mirror.slicing.platformFilter=
#p2.mirror.slicing.latestVersionOnly=false

#p2.mirror.raw=false

############## SOURCE BUNDLE CONTROL ################
# Set this property to have source bundles created and output into build repository.
# This does NOT put them in the build output (e.g., product) itself.
# Valid values are: not set, built, all.
# 		built = only source for bundles that are actually built/compiled in this run are output
#		all = all available source is collected and output
#sourceBundleMode=all

# When outputting autogenerated source bundles a feature is created to contain all the automatic
# source bundles.  Typically this feature is not needed and can be ignored. As such, it is given a default
# name and version.  These properties can be used to override the defaults.
#  sourceBundleTemplateFeature - can specify an existing feature which will be augmented to form the generated source feature
#  sourceBundleFeatureId - will be the id of generated source feature which contains all the generated source bundles, default value
#                          is sourceBundleTemplateFeature + ".source" if sourceBundleTemplateFeature is specified
#sourceBundleTemplateFeature=
#sourceBundleFeatureId=
#sourceBundleFeatureVersion=

############## BUILD NAMING CONTROL ################
# The directory into which the build elements are fetched and where
# the build takes place.
buildDirectory=${user.home}/eclipse.build

# Type of build.  Used in naming the build output.  Typically this value is
# one of I, N, M, S, ...
buildType=I

# ID of the build.  Used in naming the build output.
buildId=TestBuild

# Label for the build.  Used in naming the build output
buildLabel=${buildType}.${buildId}

# Timestamp for the build.  Used in naming the build output
timestamp=007

#The value to be used for the qualifier of a plugin or feature when you want to override the value computed by pde.
#The value will only be applied to plugin or features indicating build.properties, qualifier = context 
#forceContextQualifier=<the value for the qualifier>

#Enable / disable the generation of a suffix for the features that use .qualifier. 
#The generated suffix is computed according to the content of the feature   
#generateFeatureVersionSuffix=true

############# BASE CONTROL #############
# Settings for the base Eclipse components and Java class libraries 
# against which you are building.
# Base location for anything the build needs to compile against.  For example,
# in most RCP app or a plug-in,  the baseLocation should be the location of a previously
# installed Eclipse against which the application or plug-in code will be compiled and the RCP delta pack.

base=<path/to/parent/of/eclipse>
baseLocation=${base}/eclipse

#Folder containing repositories whose content is needed to compile against
#repoBaseLocation=${base}/repos
#Folder where the content of the repositories from ${repoBaseLocation} will be made available as a form suitable to be compiled against
#transformedRepoLocation=${base}/transformedRepos

#Os/Ws/Arch/nl of the eclipse specified by baseLocation
baseos=win32
basews=win32
basearch=x86

#this property indicates whether you want the set of plug-ins and features to be considered during the build to be limited to the ones reachable from the features / plugins being built
filteredDependencyCheck=false

#this property indicates whether the resolution should be done in development mode (i.e. ignore multiple bundles with singletons)
resolution.devMode=false

#pluginPath is a list of locations in which to find plugins and features.  This list is separated by the platform file separator (; or :)
#a location is one of:  
#- the location of the jar or folder that is the plugin or feature : /path/to/foo.jar or /path/to/foo
#- a directory that contains a /plugins or /features subdirectory
#- the location of a feature.xml, or for 2.1 style plugins, the plugin.xml or fragment.xml
#pluginPath=

skipBase=true
eclipseURL=<url for eclipse download site>
eclipseBuildId=<Id of Eclipse build to get>
eclipseBaseURL=${eclipseURL}/eclipse-platform-${eclipseBuildId}-win32.zip


############# MAP FILE CONTROL ################
# This section defines CVS tags to use when fetching the map files from the repository.
# If you want to fetch the map file from repository / location, change the getMapFiles target in the customTargets.xml

skipMaps=true
mapsRepo=:pserver:<EMAIL>/path/to/repo
mapsRoot=path/to/maps
mapsCheckoutTag=HEAD

#tagMaps=true
mapsTagTag=v${buildId}


############ REPOSITORY CONTROL ###############
# This section defines properties parameterizing the repositories where plugins, fragments
# bundles and features are being obtained from. 

# The tags to use when fetching elements to build.
# By default thebuilder will use whatever is in the maps.  
# This value takes the form of a comma separated list of repository identifier (like used in the map files) and the 
# overriding value
# For example fetchTag=CVS=HEAD, SVN=v20050101
# fetchTag=HEAD
skipFetch=true


############# JAVA COMPILER OPTIONS ##############
# The location of the Java jars to compile against.  Typically the rt.jar for your JDK/JRE
#bootclasspath=${java.home}/lib/rt.jar

# specific JRE locations to compile against. These values are used to compile bundles specifying a 
# Bundle-RequiredExecutionEnvironment. Uncomment and set values for environments that you support
#CDC-1.0/Foundation-1.0= /path/to/rt.jar
#CDC-1.1/Foundation-1.1=
#OSGi/Minimum-1.0=
#OSGi/Minimum-1.1=
#JRE-1.1=
#J2SE-1.2=
#J2SE-1.3=
#J2SE-1.4=
#J2SE-1.5=
#JavaSE-1.6=
#PersonalJava-1.1=
#PersonalJava-1.2=
#CDC-1.0/PersonalBasis-1.0=
#CDC-1.0/PersonalJava-1.0=
#CDC-1.1/PersonalBasis-1.1=
#CDC-1.1/PersonalJava-1.1=

# Specify the output format of the compiler log when eclipse jdt is used
logExtension=.log

# Whether or not to include debug info in the output jars
javacDebugInfo=false 

# Whether or not to fail the build if there are compiler errors
javacFailOnError=true

# Enable or disable verbose mode of the compiler
javacVerbose=true

# Extra arguments for the compiler. These are specific to the java compiler being used.
#compilerArg=

# Default value for the version of the source code. This value is used when compiling plug-ins that do not set the Bundle-RequiredExecutionEnvironment or set javacSource in build.properties
#javacSource=1.3

# Default value for the version of the byte code targeted. This value is used when compiling plug-ins that do not set the Bundle-RequiredExecutionEnvironment or set javacTarget in build.properties.
#javacTarget=1.1


