<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Operators Related to QNames And Nodes</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch04.html" title="How to use XPath 2.0 operators with PsychoPath"><link rel="prev" href="ch04s06.html" title="Arithmetic Functions Dates and Times"><link rel="next" href="ch04s08.html" title="Union, Intersection and Except"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Operators Related to QNames And Nodes"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Operators_Related_to_QNames_And_Nodes"></a>Operators Related to QNames And Nodes</h2></div></div></div><div class="literallayout"><p>xs:QName(&rsquo;ao&rsquo;)&nbsp;eq&nbsp;xs:QName(&rsquo;ao&rsquo;)<br>
</p></div><p>from within a Java application, in order to extract the result from the result sequence, one would have to use this code: </p><p>boolean n = ((XSBoolean)rs.first()).value(); println(n);</p><p>which returns the result of &lsquo;true&rsquo;</p></div></body></html>