<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>How to use XPath 2.0 functions with PsychoPath</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="index.html" title="usermanual"><link rel="prev" href="ch02s03s11.html" title="SequenceType Matching Expressions"><link rel="next" href="ch03s02.html" title="Constructor Functions"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="chapter" title="How to use XPath 2.0 functions with PsychoPath"><div class="titlepage"><div><div><h2 class="title"><a name="How_to_use_XPath_2.0_functions_with_PsychoPath"></a>How to use XPath 2.0 functions with PsychoPath</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><ul><li><span class="section"><a href="ch03.html#Accessors">Accessors</a></span></li><li><span class="section"><a href="ch03s02.html">Constructor Functions</a></span></li><li><span class="section"><a href="ch03s03.html">Functions on Numeric Values</a></span></li><li><span class="section"><a href="ch03s04.html">Functions to Assemble and Disassemble Strings</a></span></li><li><span class="section"><a href="ch03s05.html">Compare and Other Functions on String Values</a></span></li><li><span class="section"><a href="ch03s06.html">Functions Based on Substring Matching</a></span></li><li><span class="section"><a href="ch03s07.html">String Functions that Use Pattern Matching</a></span></li><li><span class="section"><a href="ch03s08.html">Functions on Boolean Values</a></span></li><li><span class="section"><a href="ch03s09.html">Component Extraction Functions on Durations, Dates and Times</a></span></li><li><span class="section"><a href="ch03s10.html">Functions Related to QNames</a></span></li><li><span class="section"><a href="ch03s11.html">Functions on Nodes</a></span><ul><li><span class="section"><a href="ch03s11.html#General_Functions_on_Sequences">General Functions on Sequences</a></span></li><li><span class="section"><a href="ch03s11s02.html">Functions That Test the Cardinality of Sequences</a></span></li><li><span class="section"><a href="ch03s11s03.html">Deep-Equal, Aggregate Functions, and Functions that Generate Sequences</a></span></li><li><span class="section"><a href="ch03s11s04.html">Context Functions</a></span></li></ul></li></ul></div><p>The aim of this section is to give the user an overview of the available XPath 2.0 functions that are implemented in PsychoPath. For the formal specifications, see the W3C web-site for XPath 2.0 functions and operators 
			<a class="ulink" href="http://www.w3.org/TR/xpath-functions/" target="_top">http://www.w3.org/TR/xpath-functions/</a>.
			 
		</p><div class="section" title="Accessors"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Accessors"></a>Accessors</h2></div></div></div><p>In order for PsychoPath to operate on instances of the XPath 2.0 data model, the model must expose the properties of the items it contains. It does this by defining a family of accessor functions. These functions are not available to users or applications to call directly. Instead, they are descriptions of the information that an implementation of the model must expose to applications. </p><div class="literallayout"><p>data(&lsquo;string&rsquo;)<br>
</p></div><p>from within a Java application, in order to extract the result from the result sequence, one would have to use this code: </p><p>String n = ((XSString)rs.first()).stringvalue(); println(n);</p><p>in order to get the result of &lsquo;string&rsquo;</p></div></div></body></html>