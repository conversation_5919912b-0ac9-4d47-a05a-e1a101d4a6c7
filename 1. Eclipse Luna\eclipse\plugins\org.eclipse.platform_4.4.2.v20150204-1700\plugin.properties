###############################################################################
# Copyright (c) 2000, 2015 IBM Corporation and others.
# All rights reserved. This program and the accompanying materials
# are made available under the terms of the Eclipse Public License v1.0
# which accompanies this distribution, and is available at
# http://www.eclipse.org/legal/epl-v10.html
#
# Contributors:
#     IBM Corporation - initial API and implementation
###############################################################################
pluginName=Eclipse Platform
providerName=Eclipse.org

productName=Eclipse Platform
productBlurb=Eclipse Platform\n\
\n\
Version: {1} ({2})\n\
Build id: {0}\n\
\n\
(c) Copyright Eclipse contributors and others 2000, 2015.  All rights reserved. Eclipse and the Eclipse logo are trademarks \
of the Eclipse Foundation, Inc., https://www.eclipse.org/. The Eclipse logo cannot be altered without Eclipse''s permission. \
Eclipse logos are provided for use under the Eclipse logo and trademark guidelines, https://www.eclipse.org/logotm/. \
Oracle and Java are trademarks or registered trademarks of Oracle and/or its affiliates. Other names may be trademarks of their respective owners.\n\
\n\
This product includes software developed by other open source projects including the Apache Software Foundation, https://www.apache.org/.\n

cheatsheet.actionset = Cheat Sheets
cheatsheet.item = &Cheat Sheets...
cheatsheet.category.team = Team/CVS
cheatsheet.cvs.checkout.name= Check out a CVS project
cheatsheet.cvs.checkout.desc= Learn how to connect to a CVS repository and check out a project.
cheatsheet.cvs.merge.name= Merge CVS branches
cheatsheet.cvs.merge.desc= Follow the steps for merging changes from one CVS branch into another.
shortcut.overview.tooltip = Overview
shortcut.tutorials.tooltip = Tutorials
shortcut.samples.tooltip = Samples
shortcut.whatsnew.tooltip = What's New

productIntroTitle = Welcome to Eclipse
productIntroBrandingText = Eclipse Project
introDescription-overview = The Eclipse Platform is a kind of universal tool platform - an open extensible IDE for anything and nothing in particular. 
introDescription-tutorials = Learn how to be productive using Eclipse by completing end-to-end tutorials that will guide you along the way.
introDescription-samples = Explore Eclipse by installing prefabricated samples (may require Internet connection).

trimmedwindow.label.eclipseSDK = Eclipse SDK
command.name.exit = Exit
command.name.showView = Show View
command.name.save = Save
command.name.saveAll = Save All
bindingcontext.name.dialogAndWindows = In Dialog and Windows
bindingcontext.name.windows = In Windows
bindingcontext.name.bindingView = In Binding View
bindingcontext.name.dialogs = In Dialogs
