<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Functions to Assemble and Disassemble Strings</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch03.html" title="How to use XPath 2.0 functions with PsychoPath"><link rel="prev" href="ch03s03.html" title="Functions on Numeric Values"><link rel="next" href="ch03s05.html" title="Compare and Other Functions on String Values"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Functions to Assemble and Disassemble Strings"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Functions_to_Assemble_and_Disassemble_Strings"></a>Functions to Assemble and Disassemble Strings</h2></div></div></div><div class="literallayout"><p>codepoints-to-string(0111)<br>
</p></div><p>from within a Java application, in order to extract the result from the result sequence, one would have to use this code: </p><p>String n = ((XSString)rs.first()).stringvalue(); println(n);</p><p>in order to get the result of &lsquo;o&rsquo;</p></div></body></html>