<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Context Functions</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch03s11.html" title="Functions on Nodes"><link rel="prev" href="ch03s11s03.html" title="Deep-Equal, Aggregate Functions, and Functions that Generate Sequences"><link rel="next" href="ch04.html" title="How to use XPath 2.0 operators with PsychoPath"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Context Functions"><div class="titlepage"><div><div><h3 class="title"><a name="Context_Functions"></a>Context Functions</h3></div></div></div><div class="literallayout"><p>(10&nbsp;to&nbsp;20)[position()&nbsp;=&nbsp;2]<br>
</p></div><p>from within a Java application, in order to extract the result from the result sequence, one would have to use this code: </p><p>int pos = ((XSInteger)rs.first()).intvalue(); println(pos);</p><p>in order to get the result of &lsquo;11&rsquo;</p></div></body></html>