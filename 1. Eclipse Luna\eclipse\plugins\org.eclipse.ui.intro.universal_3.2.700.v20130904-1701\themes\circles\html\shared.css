/*******************************************************************************
 * Copyright (c) 2006, 2009 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials 
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * 
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *******************************************************************************/

/* 
 * Set up general fonts, sizes and colors 
 */
body { font-family : Arial, sans-serif; }

H1, H2, H3, H4, p, a { color : #4D4D4D; }

/*
 * We are not using titles in this theme.
 */
.intro-header {
	display : none;
}

/* The label part of the folding section */
.section-title-link .section-title {
	display : inline;
}

h2 {
	font-weight : normal;
	color : #7B8694;
}

/* For regular div labels */
H4 .div-label {
	font-family: Verdana, Arial, Helvetica; 
	font-weight: bold; 
	color: #4A4D4A; 
	line-height:1.3;
}

/* For the main page content's title */
#content-header H4 .div-label {
	font-family: Verdana, Arial, Helvetica;
	color:#333333; 
	font-weight: normal; 
	letter-spacing:-0.03em;
	margin-left: 68px;
	float : none;
	clear : both;
}

/* For separators */
HR {
	width: 90%;
	align: left;
	height : 1px;
	color :  #dfdfe4;
}

/* Page description if the page has it. */
.page-description {
	display: block;
	font-family: Verdana, Arial, Helvetica; 
	line-height:1.3;
	float : none;
	clear : both;
	margin-left: 70px;
}

a {
	font-weight : bold;
	text-decoration : none;
	color : #4D4D4D;
}

/* General link labels */
a .link-label {
	font-weight : normal;
}

/* Floating link labels for navigation links */
#navigation-links a .link-label {
	font-weight : bold;
}

#navigation-links a.high-contrast .link-label {
	display : none !important;
}

/* Text in links. */
a .text {
	font-weight : normal;
}

p .group-description {
	font-family: Verdana, Arial, Helvetica; 
	font-weight : normal;
}

/* Hide the extra div in links by default. */
.link-extra-div {
	display : none;
}

/* 
 * Set up other general properties like padding/margins
 */
html, body { width : 100%; height : 100%; }

html, body, div, h1, h4, p, a { margin : 0px; padding : 0px; }

/* 
 * Page header - adding extra padding at the bottom to compensate
 * for navigation background/header overlap.
 */
#page-content #content-header {
	padding-bottom : 22px;
}

/* For regular div labels */
#page-content div H4 {
	padding : 10px;
	padding-bottom : 0px;
}

/* For the main page content's div label */
#page-content #content-header H4 {
	padding-bottom : 10px;
	padding-top : 0px;
}

/* special case for Mozilla's main content-header label.
   Mozilla 1.4 needs more room at the top */
#page-content > #content-header H4 { padding-top : 10px; }

/* Needed in IE to get shift+tab to show the active image properly */
a:active {
	border : solid 0px;
}

a img {
	border-width : 0;
	background-repeat : no-repeat;
}

/*
 * to get scrollbars working in both IE and Mozilla
 */

/*
 * to get scrollbars working in both IE and Mozilla
 */
html,body { overflow: auto; }
html>body { overflow: visible; }

/*
 * Set up the body, decorative background, and navigation for the content 
 * pages. 
 * Note: the root page handles its own background and navigation; these
 * settings primarily apply to the content pages
 */
body {
	background-color : #FFFFFF;
	background-repeat : no-repeat;
	background-position : bottom right;
}

/*
 * Hide the general-purpose groups - not using them in this theme.
 */
#extra-group1,
#extra-group2,
#extra-group3,
#extra-group4,
#extra-group5 {
	display : none;
}

/*
 * Dimensions.
 */
body, .page {
	/* since IE doesn't support min-width, try expression */
	height : 100%;
}

.page {
	background-image : url(../graphics/contentpage/background.jpg);
	background-repeat : repeat-x;
	background-position : top left;
	
	min-width : 770px;
	width:expression(document.body.clientWidth < 770? "770px": "auto" );
    min-height : 425px;
	height : expression(document.body.clientHeight < 425? "425px": "100%" );
}

#page-content {
	background-repeat : no-repeat;
	background-position : bottom right;
	height : 65%;
}

/* 
 * Lay out the navigation links 
 * (Root page does something similar for its navigation)
 */
#navigation-links {
	position : relative;
	left : 0px;
	top : 0px;
	padding-left: 12px;
	height : 118px;
	width : 100%;
}

.page > #navigation-links {
	width: 98.1%;
}

#navigation-links a {
	text-align : left;
	width : auto;
	height : 64px;
}

/*
 * Navigation links have a mixin style 'nav_link<n>' where <n> goes from
 * 1 to N. This allows us to position these fixed link slots while
 * not hand-coding the actual link IDs because they can be turned off
 * by users or products via preferences.
 */

#navigation-links a.nav_link1 {
	position: absolute;
	left : 12px;
}

#navigation-links a.nav_link2 {
	position : absolute;
	left : 76px;
}

#navigation-links a.nav_link3 {
	position : absolute;
	left : 140px;
}

#navigation-links a.nav_link4 {
	position : absolute;
	left : 204px;
}

#navigation-links a.nav_link5 {
	position : absolute;
	left : 268px;
}

#navigation-links a.nav_link6 {
	position : absolute;
	left : 332px;
}

#navigation-links a.nav_link7 {
	position: absolute;
	left : 396px;
}

#navigation-links a img {
	position : relative;
	height : 64px;
	width : 64px;
	vertical-align : center;
	horizontal-align : center;
	top : 10px;
	left : 9px;
}

#navigation-links a.high-contrast img {
	height: 32px;
	width: 32px;
	top: 5px !important;
	left: 0px !important;
}

/*
 * Adjust image position for hover version.
 */

#navigation-links a:hover img,
#navigation-links a:focus img,
#navigation-links a:active img {
	top : 2px;
	left : 0px;
}

/*
 * Navigation link label text is normally hidden. Workaround to produce the
 * same effect as display: none while still allowing screenreaders to
 * speak it.
 */
#navigation-links a .link-label { 
    display : none;
}

/*
 * Not showing description for navigation links.
 */
#navigation-links a .text { display : none; }

#navigation-links a:hover,
#navigation-links a:focus,
#navigation-links a:active {
	border-right : 0px;
	background-image: url(../graphics/icons/ctool/nav_midhov.gif);
	background-repeat: repeat-x;
	background-position: 0px 2px;
	z-index: 20;
}

#navigation-links a:hover .link-label,
#navigation-links a:focus .link-label,
#navigation-links a:active .link-label {
	display : inline;
	clear : both;
	float : left;
	text-align: right;
	position : relative;
	padding-left: 7px;
	padding-top: 7px;
	padding-right: 7px;
	margin-right: auto;
	top : -35px;
	white-space: nowrap;
	height : 32px;
	left: auto;
	width: auto;
}

/*
 * Add the right edge by using the extra div generated for links
 * and set the right edge image as the background.
 */

#navigation-links a:hover .link-extra-div,
#navigation-links a:focus .link-extra-div,
#navigation-links a:active .link-extra-div {
	display: block;
	position: absolute;
	right : -2px;
	top : 2px;
	width : 2px;
	height : 64px;
	background-image: url(../graphics/icons/ctool/nav_rightedgehov.gif);
	background-repeat: no-repeat;
}

/* properties for each of the navigation-links  */
#navigation-links a#overview img { 
	background-image : url(../graphics/icons/ctool/ov_nav_32.gif); 
}
#navigation-links a#overview:hover img,
#navigation-links a#overview:focus img,
#navigation-links a#overview:active img { 
	background-image : url(../graphics/icons/ctool/ov_nav_hover.gif); 
}

#navigation-links a#firststeps img { 
	background-image : url(../graphics/icons/ctool/fs_nav_32.gif); 
}
#navigation-links a#firststeps:hover img,
#navigation-links a#firststeps:focus img,
#navigation-links a#firststeps:active img { 
	background-image : url(../graphics/icons/ctool/fs_nav_hover.gif); }

#navigation-links a#tutorials img { 
	background-image : url(../graphics/icons/ctool/tu_nav_32.gif); 
}
#navigation-links a#tutorials:hover img,
#navigation-links a#tutorials:active img,
#navigation-links a#tutorials:focus img { 
	background-image : url(../graphics/icons/ctool/tu_nav_hover.gif); 
}

#navigation-links a#samples img { 
	background-image : url(../graphics/icons/ctool/sa_nav_32.gif); 
}
#navigation-links a#samples:hover img,
#navigation-links a#samples:active img,
#navigation-links a#samples:focus img { 
	background-image : url(../graphics/icons/ctool/sa_nav_hover.gif); 	
	left: -1px;
}

#navigation-links a#whatsnew img { 
	background-image : url(../graphics/icons/ctool/wn_nav_32.gif); 
}
#navigation-links a#whatsnew:hover img,
#navigation-links a#whatsnew:focus img,
#navigation-links a#whatsnew:active img { 
	background-image : url(../graphics/icons/ctool/wn_nav_hover.gif); 
	left: -1px;
}

#navigation-links a#migrate img { 
	background-image : url(../graphics/icons/ctool/mi_nav_32.gif); 
}
#navigation-links a#migrate:hover img,
#navigation-links a#migrate:focus img,
#navigation-links a#migrate:active img { 
	background-image : url(../graphics/icons/ctool/mi_nav_hover.gif); 
	left: -1px;
}

#navigation-links a#webresources img { 
	background-image : url(../graphics/icons/ctool/wr_nav_32.gif); 
}
#navigation-links a#webresources:hover img,
#navigation-links a#webresources:focus img,
#navigation-links a#webresources:active img { 
	background-image : url(../graphics/icons/ctool/wr_nav_hover.gif); 
	left: -1px;
}

#navigation-links a#workbench {
	position : absolute;  
	left : 494px; 
	text-align : left;
}
#navigation-links a#workbench .text { display : none; }

#navigation-links a.high-contrast#workbench:hover img,
#navigation-links a.high-contrast#workbench:focus img,
#navigation-links a.high-contrast#workbench:active img,
#navigation-links a#workbench img { 
	background-image : url(../graphics/icons/ctool/wb_nav_32.gif); 
	width : 32px; 
	height : 32px;
}

#navigation-links a#workbench:hover img,
#navigation-links a#workbench:focus img,
#navigation-links a#workbench:active img { 
	background-image : url(../graphics/icons/ctool/wb_nav_hover.gif); 
	width : 51px;
	height : 64px;
	left: -1px;
}

#action-links a.high-contrast img,
#action-links a.high-contrast:hover img,
#action-links a.high-contrast:focus img,
#action-links a.high-contrast:active img {
	display: none !important;
}

#action-links a.high-contrast .link-label {
	display: none;
}

#page-links a.high-contrast:focus .link-label,
#page-links a.high-contrast:active .link-label {
	display: block !important;
	text-decoration: underline;
	top : 5px;
}

/* 
 * Lay out the page title and description 
 */
h1, p { margin-left : 10px; } /* required in mozilla so the page description is properly indented */

/* position the page content so that the page title overlays the bottom
 * of the background image, but make sure the content is always on top 
 * (using z-index) */

#page-content {
	float : none;
	clear : both;
	text-align : center;
	position : relative;
	top : -50px;
	margin-bottom: -50px;
	z-index : 10;
}

#page-content p { 
	padding-bottom : 15px; 
	text-align : left; 
	float : none;
	clear : both;
}

/* Page content quadrants. Page content is placed in four quadrants.
 * Upper pair is separated from the bottom pair with a divider
 * to ensure bottom pair is aligned even with the uneven content
 * in the upper pair.
 */

#page-content #top-left {
  border: none; float: left; margin: 0px; padding: 0px; width: 49%;
  clear: left;
}
#page-content #top-right {
  border: none; float: right; margin: 0px; padding: 0px; width: 49%;
  clear: right;
}
/* top-bottom divider - runs the entire width to ensure
 * bottom boxes start at the same y
 */
#page-content #content-divider {
  border: none; float: none; margin: 0; padding: 0px; width: 100%;
  clear: both;
}

#page-content #bottom-left {
  border: none; float: left; margin: 0px; padding: 0px; width: 49%;
  clear: left;
}
#page-content #bottom-right {
  border: none; float: right; margin: 0px; padding: 0px; width: 49%;
  clear: right;
}

#page-content #content-header H4, .page-description {
	text-align : left;
	margin-right : 10px;
	float : none;
	clear : both;
}

#page-content * > a {
	vertical-align : middle; 
}

#page-content * a img {
	height : 57px;
	width : 57px;
	vertical-align : middle;
}	

#page-content * a .link-label {
	display : block;
	position : relative;
	top : -50px;
	left : 60px;
	margin-right: 60px;
}

#page-content * a > .link-label { left: 65px; }

#page-content * a p .text {
	display : block;
	position : relative;
	top : -45px;
	margin-bottom: -25px;
	left : 53px;
	margin-right: 53px;
}

#page-content * a p > .text { left: 58px; }

#page-content * a:hover { border-right : 5px; }

/* The following rules are for extensions in all pages. Extensions should be placed in
 * groups with the style 'content-group' and contain links with the style 'content-link'.
 * Group is important so that importance mixin style can be applied to the group that
 * uses block display. We need to see a solid rectangle around the extension, not 
 * a tight polygon around the link perimeter.
 */

.content-group {
	margin-left: 10px;
	margin-right: 10px;
	padding-left: 10px;
	padding-right: 10px;
	float : none;
	clear : both;
	text-align: left;
}

.content-link .link-label {
	font-family: Verdana, Arial, Helvetica; 
	font-weight: bold; 
	line-height:1.5;
	color: #00507C;
}

.content-link:hover .link-label {
	color: #69c; 
	text-decoration : underline;
}

.content-link .text {
	font-family: Verdana, Arial, Helvetica; 
	line-height: 1.3;
}

.categoryContentnav {
	font-family: Verdana, Arial, Helvetica; 
	font-weight: bold; 
	color: #4A4D4A; 
	line-height:1.3;
}

.contentpgNavhover {
	font-family: Verdana, Arial, Helvetica; 
	font-weight: bold; 
	color: #000; 
}

.topicList {
	font-family: Verdana, Arial, Helvetica; 
	line-height:1.75;
	color: #00507C;
}

.topicList:hover {
	color: #69c;
}

.rss-feed-link a {
	font-family: Verdana, Arial, Helvetica; 
	color: #00507C;
}

/*
 * This part is for hosting embedded document inside
 * the content area.
 */

iframe {
	position:relative;
	top:16px;
	width:100%;
	height:100%;
	padding-left:10px;
}

/* mozilla scrollbar appearing off page fix */
#page-content > iframe {
	width: 98%;
	padding-left: 2%;
}