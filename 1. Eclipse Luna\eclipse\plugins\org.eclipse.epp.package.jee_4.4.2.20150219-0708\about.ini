# about.ini
# contains information about a feature
# java.io.Properties file (ISO 8859-1 with "\" escapes)
# "%key" are externalized strings defined in about.properties
# This file does not need to be translated.

# Property "aboutText" contains blurb for "About" dialog (translated)
aboutText=%blurb

# Property "featureImage" contains path to feature image (32x32)
featureImage=javaee-ide_x32.png

# Property "windowImage" contains path to window icon (16x16)
# needed for primary features only
windowImage=javaee-ide_x16.png

# Property "aboutImage" contains path to product image (500x330 or 115x164)
# needed for primary features only
aboutImage=aboutgears_115x.png

# Property "appName" contains name of the application (translated)
# needed for primary features only
appName=Eclipse

# Property "welcomePage" contains path to welcome page (special XML-based format)
# optional
welcomPage=org.eclipse.wtp.epp.package.jee.intro\introContent.xml

# Property "welcomePerspective" contains the id of the perspective in which the
# welcome page is to be opened.
# optional
welcomePerspective=org.eclipse.wtp.epp.package.jee.intro
