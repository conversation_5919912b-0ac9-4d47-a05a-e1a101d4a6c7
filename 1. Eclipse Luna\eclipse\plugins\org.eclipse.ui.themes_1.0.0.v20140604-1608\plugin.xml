<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.4"?>
<plugin>
   <extension
         point="org.eclipse.e4.ui.css.swt.theme">
   <theme
         basestylesheeturi="css/e4_default_gtk.css"
         id="org.eclipse.e4.ui.css.theme.e4_default"
         label="%theme.aix"
         os="aix">
   </theme>
      <theme
            basestylesheeturi="css/e4_classic_winxp.css"
            id="org.eclipse.e4.ui.css.theme.e4_classic"
            label="%theme.classic">
      </theme>
    <theme
        basestylesheeturi="css/e4-dark.css"
        id="org.eclipse.e4.ui.css.theme.e4_dark"
        label="%theme.dark"
        os="linux">
    </theme>
    <theme
        basestylesheeturi="css/e4-dark_win.css"
        id="org.eclipse.e4.ui.css.theme.e4_dark"
        label="%theme.dark"
        os="win32">
    </theme>
    <theme
        basestylesheeturi="css/e4-dark_mac.css"
        id="org.eclipse.e4.ui.css.theme.e4_dark"
        label="%theme.dark"
        os="macosx">
    </theme>
      <theme
            basestylesheeturi="css/e4_default_gtk.css"
            id="org.eclipse.e4.ui.css.theme.e4_default"
            label="%theme.gtk"
            os="linux">
      </theme>
   <theme
         basestylesheeturi="css/e4_classic_winxp.css"
         id="org.eclipse.e4.ui.css.theme.e4_default"
         label="%theme.hpux"
         os="hpux">
   </theme>
      <theme
            basestylesheeturi="css/e4_default_mac.css"
            id="org.eclipse.e4.ui.css.theme.e4_default"
            label="%theme.mac"
            os="macosx">
      </theme>
   <theme
         basestylesheeturi="css/e4_default_gtk.css"
         id="org.eclipse.e4.ui.css.theme.e4_default"
         label="%theme.solaris"
         os="solaris">
   </theme>
      <theme
            basestylesheeturi="css/e4_default_win7.css"
            id="org.eclipse.e4.ui.css.theme.e4_default"
            label="%theme.win7"
            os="win32"
            os_version="6.1">
      </theme>
      <theme
            basestylesheeturi="css/e4_default_winxp_blu.css"
            id="org.eclipse.e4.ui.css.theme.e4_default.xpblu"
            label="%theme.winxpBlue"
            os="win32">
      </theme>
	  <theme
            basestylesheeturi="css/e4_default_winxp_olv.css"
            id="org.eclipse.e4.ui.css.theme.e4_default.xpolive"
            label="%theme.winxpOlive"
            os="win32">
      </theme>
   <theme
         basestylesheeturi="css/e4_classic_win7.css"
         id="org.eclipse.e4.ui.css.theme.e4_classic"
         label="%theme.win7Classic"
         os="win32"
         os_version="6.1">
   </theme>
   <theme
		basestylesheeturi="css/high-contrast.css"
        id="org.eclipse.e4.ui.css.theme.high-contrast"
        label="%theme.high-contrast">
   </theme>

   <themeAssociation
		themeId="org.eclipse.e4.ui.css.theme.e4_classic"
		colorAndFontId="org.eclipse.ui.defaultTheme">
   </themeAssociation>
   <stylesheet
         uri="css/dark/e4-dark_preferencestyle.css">
      <themeid
            refid="org.eclipse.e4.ui.css.theme.e4_dark"></themeid>
   </stylesheet>

   </extension>

</plugin>
