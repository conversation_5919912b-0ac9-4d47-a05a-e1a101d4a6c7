/*******************************************************************************
 * Copyright (c) 2006, 2010 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials 
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * 
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *******************************************************************************/

/* Hide the extra div for links in the normal state. */
a .link-extra-div {
	display: none;
}

/* Link label properties */
#page-links a .link-label {
	top: -1.2em;
	color:#00507C;	
    font-family:Verdana,Arial,Helvetica;
    font-weight:bold;
}

#page-links a:hover .link-label {
	color: #69c; 
	text-decoration : underline;
}

#page-links a {
    width : 45%;
    float : left;
    margin-left : 3%; 
	margin-bottom : 35px;
}

/*
 * Set up the content for the root page.
 */
html, body {	
	overflow : auto;
	overflow-clip: rect(0, auto, auto, 0);
	background-color : none;
	background-image : url("../graphics/rootpage/background.jpg");
	background-position : top left;
	background-repeat : no-repeat;
}

#root {
    background-image : url("../graphics/rootpage/root_banner.jpg");
	background-position : top left;
	background-repeat : repeat-x;
}

#branding {
	position: absolute;
	top : 0px;
	left : 0px;
}

/* 
 * Set up the navigation bar.  It should be centered in the middle
 * of the page
*/

.intro-header {
    padding-top : 90px;
    margin-left : 7%;
    margin-bottom : 40px;
}

/* For the main page content's title */
.intro-header h1 {
	font-family: Verdana, Arial, Helvetica;
	color:#333333; 
	font-weight: normal; 
	letter-spacing:-0.03em;
}

#page-links a img {
	height : 48px;
	width : 64px;
	vertical-align : middle;
}

#page-links a span {
	display : block;
}

#page-links a span.link-label {
	position : relative;
	top : -45px;
	left : 60px;
	margin-right: 60px;
	font-family: Verdana, Arial, Helvetica; 
	font-weight: bold; 
	line-height:1.5;
	color: #00507C;
}

#page-links a p .text {
    left: 51px; 
	display : block;
	position : relative;
	top : -40px;
	margin-bottom: -25px;
	margin-right: 53px;
	font-family: Verdana, Arial, Helvetica; 
	line-height: 1.3;
}

#page-links a .background-image {
	display: none;
}

#page-links a .link-extra-div {
	display :none;
}

.content-img {
    padding-left: 15px;
}

/* Link images */
#page-links a#overview .content-img { background-image : url("../graphics/rootpage/overview48.png"); }
#page-links a#overview:hover .content-img,
#page-links a#overview:active .content-img,
#page-links a#overview:focus .content-img { background-image : url("../graphics/rootpage/overview48_hov.png"); }

#page-links a#tutorials .content-img { background-image : url("../graphics/rootpage/tutorials48.png"); }
#page-links a#tutorials:hover .content-img,
#page-links a#tutorials:active .content-img,
#page-links a#tutorials:focus .content-img { background-image : url("../graphics/rootpage/tutorials48_hov.png"); }

#page-links a#samples .content-img { background-image : url("../graphics/rootpage/samples48.png"); }
#page-links a#samples:hover .content-img,
#page-links a#samples:active .content-img,
#page-links a#samples:focus .content-img { background-image : url("../graphics/rootpage/samples48_hov.png"); }

#page-links a#whatsnew .content-img { background-image : url("../graphics/rootpage/whatsnew48.png"); }
#page-links a#whatsnew:hover .content-img,
#page-links a#whatsnew:active .content-img,
#page-links a#whatsnew:focus .content-img { background-image : url("../graphics/rootpage/whatsnew48_hov.png"); }

#page-links a#firststeps .content-img { background-image : url("../graphics/rootpage/firststeps48.png"); }
#page-links a#firststeps:hover .content-img,
#page-links a#firststeps:active .content-img,
#page-links a#firststeps:focus .content-img { background-image : url("../graphics/rootpage/firststeps48_hov.png"); }

#page-links a#migrate .content-img { background-image : url("../graphics/rootpage/migrate48.png"); }
#page-links a#migrate:hover .content-img,
#page-links a#migrate:active .content-img,
#page-links a#migrate:focus .content-img { background-image : url("../graphics/rootpage/migrate48_hov.png"); }

#page-links a#webresources .content-img { background-image : url("../graphics/rootpage/webresources48.png"); }
#page-links a#webresources:hover .content-img,
#page-links a#webresources:active .content-img,
#page-links a#webresources:focus .content-img { background-image : url("../graphics/rootpage/webresources48_hov.png"); }

/* Hack for IE6, which cannot display png files with alpha channel transparency */

* html #page-links a#overview .content-img { background-image : url("../graphics/rootpage/overview48.gif"); }
* html #page-links a#overview:hover .content-img,
* html #page-links a#overview:active .content-img,
* html #page-links a#overview:focus .content-img { background-image : url("../graphics/rootpage/overview48_hov.gif"); }

* html #page-links a#tutorials .content-img { background-image : url("../graphics/rootpage/tutorials48.gif"); }
* html #page-links a#tutorials:hover .content-img,
* html #page-links a#tutorials:active .content-img,
* html #page-links a#tutorials:focus .content-img { background-image : url("../graphics/rootpage/tutorials48_hov.gif"); }

* html #page-links a#samples .content-img { background-image : url("../graphics/rootpage/samples48.gif"); }
* html #page-links a#samples:hover .content-img,
* html #page-links a#samples:active .content-img,
* html #page-links a#samples:focus .content-img { background-image : url("../graphics/rootpage/samples48_hov.gif"); }

* html #page-links a#whatsnew .content-img { background-image : url("../graphics/rootpage/whatsnew48.gif"); }
* html #page-links a#whatsnew:hover .content-img,
* html #page-links a#whatsnew:active .content-img,
* html #page-links a#whatsnew:focus .content-img { background-image : url("../graphics/rootpage/whatsnew48_hov.gif"); }

* html #page-links a#firststeps .content-img { background-image : url("../graphics/rootpage/firststeps48.gif"); }
* html #page-links a#firststeps:hover .content-img,
* html #page-links a#firststeps:active .content-img,
* html #page-links a#firststeps:focus .content-img { background-image : url("../graphics/rootpage/firststeps48_hov.gif"); }

* html #page-links a#migrate .content-img { background-image : url("../graphics/rootpage/migrate48.gif"); }
* html #page-links a#migrate:hover .content-img,
* html #page-links a#migrate:active .content-img,
* html #page-links a#migrate:focus .content-img { background-image : url("../graphics/rootpage/migrate48_hov.gif"); }

* html #page-links a#webresources .content-img { background-image : url("../graphics/rootpage/webresources48.gif"); }
* html #page-links a#webresources:hover .content-img,
* html #page-links a#webresources:active .content-img,
* html #page-links a#webresources:focus .content-img { background-image : url("../graphics/rootpage/webresources48_hov.gif"); }

/* End hack for IE6 */

/*
 * Not using action links.
 */
#action-links {
	display: none;
}

/*
* Workbench
*/

#page-links a#workbench:hover .link-label
{
    color : #FFEC89;
    text-decoration : none;
}

#workbench p span {
    display : none;
}

* html #page-links a#workbench .content-img {
    background-image:url(../graphics/icons/ctool/wb_nav_32.gif);
}

#page-links a#workbench .content-img {
    background-image:url(../graphics/icons/ctool/wb_nav_32.gif);
    display:block;
    color : white;
    height:32px;
    margin:5px auto 0;
    width:32px;
    background-repeat:no-repeat;
    border-width:0;
}

#page-links a#workbench .link-label {
   position : static;
   margin-right : 0px;
   font-weight : bold;	
   color : white;
   font-family:Arial,sans-serif;
}

#page-links a#workbench .text {
   display : none;
}

#page-links a#workbench {
    position : absolute;
    right : 20px;
    top : 0px;
    width : auto;
    text-align:center;
    margin-bottom : 0px;
}

#page-links a#workbench img {
    padding : 0px;
}

#page-links a#workbench span {
    margin-top : 0px;
    line-height : normal;
}

#page-links a#workbench .link-label {
    font-size:8pt;
    color:white;
    font-weight:bold;
    text-align:center;
    margin-left: 0;
}