/*******************************************************************************
 * Copyright (c) 2005, 2006 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials 
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * 
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *******************************************************************************/

#page-links a .link-label, #action-links a .link-label {
	font-weight : 600;
	color : #E5E5E5;
}
#page-links a p .text, #action-links a p .text {
	font-weight : 500;
	color : #E5E5E5;
}

/*
 * Turn off shared backround image.
 */

.page {
	background-image: none;
}

/*
 * Set up the content for the root page.
 */
body {
	min-width : 770px;
	/* since IE doesn't support min-width, use expression */
	width:expression(document.body.clientWidth < 770? "770px": "auto" );
	background-image : url(../graphics/root/background.jpg);
	background-repeat : no-repeat;
	background-position : top left;
	background-color : #7169D1;
}
#root {
/*
	background-image : url(../graphics/root/brandmark.gif);
*/
	background-repeat : no-repeat;
	background-position : bottom left;
	min-height : 450px;
	height : 100%;
	height : expression(document.body.clientHeight < 450? "450px": "100%" );
}

#branding {
	position: absolute;
	bottom : 20px;
	left : 20px;
}

/*
 * We will not use the general-purpose group1 used in
 * other pages for a curve image.
 */

#extra-group1 {
	display : none;
}
/* 
 * Set up the navigation bar.  It should be centered in the middle
 * of the page
 */
#links-background {
	background-image : url(../graphics/root/dots.gif);
	background-repeat : repeat-x;
	width : 100%;
	height : 177px;
	margin-top : 18%;
	margin-bottom : auto;
	text-align : center;
}
/* specify a width for Moz so we can center.  
 * **Important** If additional links are added, we will have to increase this width 
 * to accomodate them, otherwise they will wrap to a new line 
 */

#links-background > #page-links {
	width: 33em;
	margin: 0 auto;
}

#page-links {
	position : relative;
	top : 50px;
	text-align: center;
}
#page-links a {
	position : relative;
	width : 86px;
	margin-left : 1em;
	margin-right : 1em;
	text-align : center;
	vertical-align : top;
}
/* float left for Moz so the items all appear inline */
#page-links > a {
	float : left;
	position : relative;
}
#page-links a img {
	height : 82px;
	width : 82px;
	vertical-align : middle;
}
/* remove the hover image from the flow of the document,
   so it doesn't take up space and change the position
   of the link label and descriptions */
#page-links a .background-image {
	position : absolute;
}
/* properly align the link label and text based on class (left vs. right) */
#page-links a:hover {
	/* This is needed for IE to force the hover pseudo selectors below to work.*/
	padding : 0 em;
}
/* Hide both the label and the description of the link and remove them from static HTML flow, until user hovers over link */
/* First, set the width of both the label and the description to a max of 15 em. */
/* This can be changed when translated to different country locals. */
#page-links a span {
	width : 16em;
}
/* Set up left links orientation first. */
#page-links a.left:link .link-label,
#page-links a.left:visited .link-label {
	display: none;
}
#page-links a.left:hover .link-label,
#page-links a.left:focus .link-label,
#page-links a.left:active .link-label {
	text-align: left;
	display: block;
	position: absolute;
	top : 120 %;
	left : 0;
}
/* hide description and remove it from static HTML flow, until user hovers over link */
#page-links a.left:link .text,
#page-links a.left:visited .text {
	display: none;
}
#page-links a.left:hover .text,
#page-links a.left:focus .text,
#page-links a.left:active .text {
	text-align: left;
	display: block;
	position: absolute;
	left : 0;
	top: 145%;
}
/* Set up right links orientation now. */
#page-links a.right:link .link-label,
#page-links a.right:visited .link-label {
	display: none;
}
#page-links a.right:hover .link-label,
#page-links a.right:focus .link-label,
#page-links a.right:active .link-label {
	text-align: right;
	display: block;
	position: absolute;
	top : 120 %;
	right : 0;
}
/* hide description and remove it from static HTML flow, until user hovers over link */
#page-links a.right:link .text,
#page-links a.right:visited .text {
	display: none;
}
#page-links a.right:hover .text,
#page-links a.right:focus .text,
#page-links a.right:active .text {
	text-align: right;
	display: block;
	position: absolute;
	right : 0;
	top: 145%;
}
/* properties for each of the page-links  */
#page-links a#overview img { background-image : url(../graphics/icons/etool/overview72.gif); }
#page-links a:hover#overview img { background-image : url(../graphics/icons/ctool/overview72.gif); }

#page-links a#tutorials img { background-image : url(../graphics/icons/etool/tutorials72.gif); }
#page-links a:hover#tutorials img { background-image : url(../graphics/icons/ctool/tutorials72.gif); }

#page-links a#samples img { background-image : url(../graphics/icons/etool/samples72.gif); }
#page-links a:hover#samples img { background-image : url(../graphics/icons/ctool/samples72.gif); }

#page-links a#whatsnew img { background-image : url(../graphics/icons/etool/whatsnew72.gif); }
#page-links a:hover#whatsnew img { background-image : url(../graphics/icons/ctool/whatsnew72.gif); }

#page-links a#firststeps img { background-image : url(../graphics/icons/etool/firsteps72.gif); }
#page-links a:hover#firststeps img { background-image : url(../graphics/icons/ctool/firsteps72.gif); }

#page-links a#webresources img { background-image : url(../graphics/icons/etool/webrsrc72.gif); }
#page-links a:hover#webresources img { background-image : url(../graphics/icons/ctool/webrsrc72.gif); }

#page-links a#migrate img { background-image : url(../graphics/icons/etool/migrate72.gif); }
#page-links a:hover#migrate img { background-image : url(../graphics/icons/ctool/migrate72.gif); }

/*
 * Set up the action links
 */
#action-links {
	width : 98%;
	position : absolute;
	left : 0px;
	top : 20px;
}
#action-links a#workbench {
	position : absolute;
	top : -16px;
	right : -8px;
	text-align : right;
}
#action-links a .background-image,
#action-links a #workbench_img {
	height : 53px;
	width : 53px;
	text-align : center;
	vertical-align : top;
}
/* special case for mozilla */
#action-links a > .background-image,
#action-links a > #workbench_img {
	vertical-align : middle;
}
/* remove the hover image from the flow of the document,
   so it doesn't take up space and change the position
   of the main image */
#action-links a .background-image {
	position : absolute;
}

#action-links a#workbench .background-image {
	background-image : url(../graphics/icons/etool/wb48.gif);
}

#action-links a#workbench:hover .background-image,
#action-links a#workbench:focus .background-image,
#action-links a#workbench:active .background-image {
	background-image : url(../graphics/icons/ctool/wb48.gif);
	visibility : visible;
	position: absolute;
	top: 0px;
	right: 0px;	
}
/* hide the link and description until users hover over the link */
#action-links a p .text, #action-links a .link-label {
	display : none;
}
#action-links a:hover .link-label,
#action-links a:focus .link-label,
#action-links a:active .link-label {
	display : block;
	width : 16em;
	margin-left : 10px;
}
#action-links a:hover p .text,
#action-links a:focus p .text,
#action-links a:active p .text {
	display : block;
	width : 16em;
}
#action-links a:hover,
#action-links a:focus,
#action-links a:active {
	border : 0px;
}
