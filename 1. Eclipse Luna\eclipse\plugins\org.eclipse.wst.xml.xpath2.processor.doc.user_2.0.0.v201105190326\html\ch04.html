<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>How to use XPath 2.0 operators with PsychoPath</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="index.html" title="usermanual"><link rel="prev" href="ch03s11s04.html" title="Context Functions"><link rel="next" href="ch04s02.html" title="Comparison of Numeric Values"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="chapter" title="How to use XPath 2.0 operators with PsychoPath"><div class="titlepage"><div><div><h2 class="title"><a name="How_to_use_XPath_2.0_operators_with_PsychoPath"></a>How to use XPath 2.0 operators with PsychoPath</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><ul><li><span class="section"><a href="ch04.html#Operators_on_Numeric_Values">Operators on Numeric Values</a></span></li><li><span class="section"><a href="ch04s02.html">Comparison of Numeric Values</a></span></li><li><span class="section"><a href="ch04s03.html">Operators on Boolean Values</a></span></li><li><span class="section"><a href="ch04s04.html">Comparisons of Duration, Date and Time Values</a></span></li><li><span class="section"><a href="ch04s05.html">Arithmetic Functions on Durations</a></span></li><li><span class="section"><a href="ch04s06.html">Arithmetic Functions Dates and Times</a></span></li><li><span class="section"><a href="ch04s07.html">Operators Related to QNames And Nodes</a></span></li><li><span class="section"><a href="ch04s08.html">Union, Intersection and Except</a></span></li><li><span class="section"><a href="ch04s09.html">Operators that Generate Sequences</a></span></li></ul></div><p>The aim of this section is to give the user an overview of the available XPath 2.0 operators that are implemented in PsychoPath. For the formal specifications, see the W3C web-site for XPath 2.0 functions and operators 
			<a class="ulink" href="http://www.w3.org/TR/xpath-functions/" target="_top">http://www.w3.org/TR/xpath-functions/</a>. 
		</p><p>
			 </p><div class="section" title="Operators on Numeric Values"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Operators_on_Numeric_Values"></a>Operators on Numeric Values</h2></div></div></div><div class="literallayout"><p>xs:integer(4)&nbsp;+&nbsp;xs:integer(3)<br>
</p></div><p>from within a Java application, in order to extract the result from the result sequence, one would have to use this code: </p><p>Integer n = ((XSInteger)rs.first()).integervalue(); println(n);</p><p>in order to get the result of &lsquo;7&rsquo;</p></div></div></body></html>