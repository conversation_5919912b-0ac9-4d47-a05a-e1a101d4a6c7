p.Code {
	display: block;
	text-align: left;
	text-indent: 0.00pt;
	margin-top: 0.000000pt;
	margin-bottom: 0.000000pt;
	margin-right: 0.000000pt;
	margin-left: 15pt;
	font-weight: normal;
	font-style: normal;
	color: #4444CC;
	text-decoration: none;
	vertical-align: baseline;
	text-transform: none;
	font-family: "Courier New", Courier, monospace;
}

H6.CaptionFigColumn {
	display: block;
	text-align: left;
	text-indent: 0.000000pt;
	margin-top: 3.000000pt;
	margin-bottom: 11.000000pt;
	margin-right: 0.000000pt;
	margin-left: 0.000000pt;
	font-size: 75%;
	font-weight: bold;
	font-style: Italic;
	color: #000000;
	text-decoration: none;
	vertical-align: baseline;
	text-transform: none;
}

p.Note {
	display: block;
	text-align: left;
	text-indent: 0pt;
	margin-top: 19.500000pt;
	margin-bottom: 19.500000pt;
	margin-right: 0.000000pt;
	margin-left: 30pt;
	font-size: 110%;
	font-weight: normal;
	font-style: Italic;
	color: #000000;
	text-decoration: none;
	vertical-align: baseline;
	text-transform: none;
}

EM.UILabel {
	font-weight: Bold;
	font-style: normal;
	text-decoration: none;
	vertical-align: baseline;
	text-transform: none;
}

EM.CodeName {
	font-weight: Bold;
	font-style: normal;
	text-decoration: none;
	vertical-align: baseline;
	text-transform: none;
	font-family: "Courier New", Courier, monospace;
}

UL.NavList {
	margin-left: 1.5em;
	padding-left: 0px;
	list-style-type: none;
}

body,html {
	border: 0px
}

/* following font face declarations need to be removed for DBCS */
body,h1,h2,h3,h4,h5,h6,p,table,td,caption,th,ul,ol,dl,li,dd,dt {
	font-family: Arial, Helvetica, sans-serif;
	color: #000000
}

pre,code {
	font-family: "Courier New", Courier, monospace;
}

div.literallayout {
	font-family: "Courier New", Courier, monospace;
	background-color: silver;
	border: thin;
	border-style: dashed;
	color: black;
	padding-left: 5;
}
div.literallayout p {
	font-family: "Courier New", Courier, monospace;
}
/* end font face declarations */
@media print {
	html {
		font-size: 12pt
	}
}

body {
	font-size: 83%;
	background: #FFFFFF;
	margin-bottom: 1em
}

h1 {
	font-size: 180%;
	margin-top: 5px;
	margin-bottom: 1px
}

h2 {
	font-size: 140%;
	margin-top: 25px;
	margin-bottom: 3px
}

h3 {
	font-size: 110%;
	margin-top: 20px;
	margin-bottom: 3px
}

h4 {
	font-size: 100%;
	margin-top: 20px;
	margin-bottom: 3px;
	font-style: italic
}

p {
	margin-top: 10px;
	margin-bottom: 10px
}

pre {
	font-size: 93%;
	margin-left: 6;
	color: #4444CC
}

code {
	font-size: 93%;
}

table {
	font-size: 100%
}  /* needed for quirks mode */
a:link {
	color: #0000FF
}

a:hover {
	color: #000080
}

a:visited {
	text-decoration: underline
}

ul {
	margin-top: 10px;
	margin-bottom: 10px;
}

li {
	margin-top: 5px;
	margin-bottom: 5px;
}

li p {
	margin-top: 5px;
	margin-bottom: 5px;
}

ol {
	margin-top: 10px;
	margin-bottom: 10px;
}

dl {
	margin-top: 10px;
	margin-bottom: 10px;
}

dt {
	margin-top: 5px;
	margin-bottom: 5px;
	font-weight: bold;
}

dd {
	margin-top: 5px;
	margin-bottom: 5px;
}

strong {
	font-weight: bold
}

em {
	font-style: italic
}

var {
	font-style: italic
}

div.revision {
	border-left-style: solid;
	border-left-width: thin;
	border-left-color: #7B68EE;
	padding-left: 5;
}

th {
	font-weight: bold;
}

pre.programlisting {
	background-color: silver;
	border: thin;
	border-style: dashed;
	color: black;
	padding-left: 5;
}