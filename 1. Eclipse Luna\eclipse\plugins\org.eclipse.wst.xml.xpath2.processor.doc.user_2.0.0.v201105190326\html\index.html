<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>usermanual</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="next" href="ch01.html" title="Introduction"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="book" title="usermanual"><div class="titlepage"><div><div><h1 class="title"><a name="N10001"></a>usermanual</h1></div></div><hr></div><div class="toc"><p><b>Table of Contents</b></p><ul><li><span class="chapter"><a href="ch01.html">Introduction</a></span><ul><li><span class="section"><a href="ch01.html#Getting_PsychoPath">Getting PsychoPath</a></span></li></ul></li><li><span class="chapter"><a href="ch02.html">How to feed Psychopath XPath expressions</a></span><ul><li><span class="section"><a href="ch02.html#Non-Schema_Aware">Non-Schema Aware</a></span></li><li><span class="section"><a href="ch02s02.html">Schema Aware</a></span></li><li><span class="section"><a href="ch02s03.html">How to use the XPath 2.0 grammar with PsychoPath</a></span><ul><li><span class="section"><a href="ch02s03.html#Constants">Constants</a></span></li><li><span class="section"><a href="ch02s03s02.html">Path expressions</a></span></li><li><span class="section"><a href="ch02s03s03.html">Axis steps</a></span></li><li><span class="section"><a href="ch02s03s04.html">Set difference, intersection and Union</a></span></li><li><span class="section"><a href="ch02s03s05.html">Arithmetic Expressions</a></span><ul><li><span class="section"><a href="ch02s03s05.html#Unary">Unary</a></span></li><li><span class="section"><a href="ch02s03s05s02.html">Multiplication and Division:</a></span></li><li><span class="section"><a href="ch02s03s05s03.html">Addition and Subtraction:</a></span></li></ul></li><li><span class="section"><a href="ch02s03s06.html">Range expressions</a></span></li><li><span class="section"><a href="ch02s03s07.html">Comparisons</a></span></li><li><span class="section"><a href="ch02s03s08.html">Conditional Expressions</a></span></li><li><span class="section"><a href="ch02s03s09.html">Quantified Expressions</a></span></li><li><span class="section"><a href="ch02s03s10.html">And, Or Expressions</a></span></li><li><span class="section"><a href="ch02s03s11.html">SequenceType Matching Expressions</a></span></li></ul></li></ul></li><li><span class="chapter"><a href="ch03.html">How to use XPath 2.0 functions with PsychoPath</a></span><ul><li><span class="section"><a href="ch03.html#Accessors">Accessors</a></span></li><li><span class="section"><a href="ch03s02.html">Constructor Functions</a></span></li><li><span class="section"><a href="ch03s03.html">Functions on Numeric Values</a></span></li><li><span class="section"><a href="ch03s04.html">Functions to Assemble and Disassemble Strings</a></span></li><li><span class="section"><a href="ch03s05.html">Compare and Other Functions on String Values</a></span></li><li><span class="section"><a href="ch03s06.html">Functions Based on Substring Matching</a></span></li><li><span class="section"><a href="ch03s07.html">String Functions that Use Pattern Matching</a></span></li><li><span class="section"><a href="ch03s08.html">Functions on Boolean Values</a></span></li><li><span class="section"><a href="ch03s09.html">Component Extraction Functions on Durations, Dates and Times</a></span></li><li><span class="section"><a href="ch03s10.html">Functions Related to QNames</a></span></li><li><span class="section"><a href="ch03s11.html">Functions on Nodes</a></span><ul><li><span class="section"><a href="ch03s11.html#General_Functions_on_Sequences">General Functions on Sequences</a></span></li><li><span class="section"><a href="ch03s11s02.html">Functions That Test the Cardinality of Sequences</a></span></li><li><span class="section"><a href="ch03s11s03.html">Deep-Equal, Aggregate Functions, and Functions that Generate Sequences</a></span></li><li><span class="section"><a href="ch03s11s04.html">Context Functions</a></span></li></ul></li></ul></li><li><span class="chapter"><a href="ch04.html">How to use XPath 2.0 operators with PsychoPath</a></span><ul><li><span class="section"><a href="ch04.html#Operators_on_Numeric_Values">Operators on Numeric Values</a></span></li><li><span class="section"><a href="ch04s02.html">Comparison of Numeric Values</a></span></li><li><span class="section"><a href="ch04s03.html">Operators on Boolean Values</a></span></li><li><span class="section"><a href="ch04s04.html">Comparisons of Duration, Date and Time Values</a></span></li><li><span class="section"><a href="ch04s05.html">Arithmetic Functions on Durations</a></span></li><li><span class="section"><a href="ch04s06.html">Arithmetic Functions Dates and Times</a></span></li><li><span class="section"><a href="ch04s07.html">Operators Related to QNames And Nodes</a></span></li><li><span class="section"><a href="ch04s08.html">Union, Intersection and Except</a></span></li><li><span class="section"><a href="ch04s09.html">Operators that Generate Sequences</a></span></li></ul></li></ul></div></div></body></html>