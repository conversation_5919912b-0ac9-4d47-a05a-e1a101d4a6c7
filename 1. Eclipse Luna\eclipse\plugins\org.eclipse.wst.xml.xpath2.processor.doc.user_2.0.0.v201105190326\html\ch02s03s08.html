<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Conditional Expressions</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch02s03.html" title="How to use the XPath 2.0 grammar with PsychoPath"><link rel="prev" href="ch02s03s07.html" title="Comparisons"><link rel="next" href="ch02s03s09.html" title="Quantified Expressions"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Conditional Expressions"><div class="titlepage"><div><div><h3 class="title"><a name="Conditional_Expressions"></a>Conditional Expressions</h3></div></div></div><p>XPath 2.0 allows a conditional expression of the form 
					<span class="italic">if ( E1 ) then E2 else E3</span>. For example, 
					<span class="italic">if (@discount) then @discount else 0</span> returns the value of the discount attribute if it is present, or zero otherwise. 
				</p><p>
					 </p></div></body></html>