/*******************************************************************************
 * Copyright (c) 2006 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials 
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * 
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *******************************************************************************/

/* show the "selected" image for this page */
#navigation-links a#samples img, 
#navigation-links a#samples:hover img,
#navigation-links a#samples:focus img,
#navigation-links a#samples:active img {
	background-image : url(../graphics/icons/ctool/sa_nav_64.gif); 
	top : 2px; 
	left : -3px;
}

#navigation-links {
	background-image: url(../graphics/contentpage/sa_banner.jpg);
}

#navigation-links a:hover#samples .link-label,
#navigation-links a:focus#samples .link-label,
#navigation-links a:active#samples .link-label {
	display : none;
}

#navigation-links a:hover#samples,
#navigation-links a:focus#samples,
#navigation-links a:active#samples {
	background-image : none;
}

#navigation-links a:hover#samples .link-extra-div,
#navigation-links a:focus#samples .link-extra-div,
#navigation-links a:active#samples .link-extra-div {
	background-image : none;
}