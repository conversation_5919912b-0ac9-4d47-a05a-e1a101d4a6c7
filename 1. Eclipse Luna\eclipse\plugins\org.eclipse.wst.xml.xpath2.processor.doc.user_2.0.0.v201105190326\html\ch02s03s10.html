<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>And, Or Expressions</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch02s03.html" title="How to use the XPath 2.0 grammar with PsychoPath"><link rel="prev" href="ch02s03s09.html" title="Quantified Expressions"><link rel="next" href="ch02s03s11.html" title="SequenceType Matching Expressions"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="And, Or Expressions"><div class="titlepage"><div><div><h3 class="title"><a name="And.2C_Or_Expressions"></a>And, Or Expressions</h3></div></div></div><p>The expression 
					<span class="italic">E1 and E2</span> returns true if the effective boolean values of E1 and E2 are both true. The expression 
					<span class="italic">E1 or E2</span> returns true if the effective boolean values of either or both of E1 and E2 are true. 
				</p><p>
					<span class="bold"><strong>
						<span class="italic">Example:</span>
					</strong></span> (for a truth table) 
				</p><div class="literallayout"><p>1&nbsp;and&nbsp;1<br>
</p></div><p> 

					<span class="bold"><strong>result:</strong></span> 
				</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>xs:boolean: true</p></li></ol></div><p>
					 </p><div class="literallayout"><p>1&nbsp;and&nbsp;0<br>
</p></div><p> 

					<span class="bold"><strong>result:</strong></span> 
				</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>xs:boolean: false</p></li></ol></div><p>
					 </p><div class="literallayout"><p>1&nbsp;or&nbsp;0<br>
</p></div><p> 

					<span class="bold"><strong>result:</strong></span> 
				</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>xs:boolean: true</p></li></ol></div><p>
					 </p><div class="literallayout"><p>0&nbsp;or&nbsp;1<br>
</p></div><p> 

					<span class="italic">'result: '</span> 
				</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>xs:boolean: true</p></li></ol></div><p>
					 </p></div></body></html>