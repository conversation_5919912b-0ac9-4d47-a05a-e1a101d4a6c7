<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1"/>
<style type="text/css">
  table.platforms {
    border-width: 1px;
    border-spacing: 0px;
    border-style: solid;
    border-collapse: separate;
  }
  table.platforms th {
    border-width: 1px;
    padding: 3px;
    border-style: inset;
    border-color: black;
    background-color: #B9A9FF;
  }
  table.platforms td {
    border-width: 1px 1px 1px 1px;
    padding: 3px 3px 3px 3px;
    border-style: inset inset inset inset;
    border-color: gray gray gray gray;
  }
  table.platforms tr.c0 td {
    background-color: #FDFDFD;
  }
  table.platforms tr.c1 td {
    background-color: #F4EEFF;
  }
</style>
<title>Eclipse Project Release Notes 4.4</title>
</head>
<body>

<h1>Eclipse Project Release Notes</h1>
<p>Release 4.4.0<br/>
Last revised June 5, 2014</p>

<p align="left"><strong>This software is OSI Certified Open Source Software.<br/>
OSI Certified is a certification mark of the Open Source Initiative.&nbsp;</strong></p>
<blockquote>
  <p align="left"><a href="#TargetOperatingEnvironments">1. Target Operating
  Environments</a><br/>
  <a href="#Compatibility">2. Compatibility with Previous
  Releases</a><br/>
  <a href="#KnownIssues">3. Known Issues</a><br/>
  <a href="#RunningEclipse">4. Running Eclipse</a><br/>
  <a href="#Upgrading">5. Upgrading a Workspace from a Previous Release</a><br/>
  <a href="#InteroperabilityWithPreviousReleases">6. Interoperability with
  Previous Releases</a><br/>
  </p>
</blockquote>

<h2>1. <a name="TargetOperatingEnvironments"></a>Target Operating Environments</h2>
<p>In order to remain current, each Eclipse Project release targets reasonably current
  operating environments.</p>
<p>Most of the Eclipse SDK is &quot;pure&quot; Java code and has no direct dependence
  on the underlying operating system. The chief dependence is therefore on the
  Java Platform itself. Portions are targeted to specific classes of operating
  environments, requiring their source code to only reference facilities available
  in particular class libraries (e.g. J2ME Foundation 1.1, J2SE 1.4, Java 5, etc).</p>
<p>
  In general, the 4.4 release of the Eclipse Project is developed on a mix
  of Java SE 6 and Java SE 7 VMs. As such, the Eclipse SDK as a whole
  is targeted at all modern, desktop Java VMs. Most functionality is available for
  Java SE 6 level development everywhere, and extended development capabilities are made
  available on the VMs that support them.</p>
<p><a href="#appendix">Appendix 1</a> contains a table that indicates the class
  library level required for each bundle.</p>
<p>There are many different implementations of the Java Platform running atop
  a variety of operating systems. We focus our testing on a handful of
  popular combinations of operating system and Java Platform; these are our <em>reference
  platforms</em>. Eclipse undoubtedly runs fine in many operating environments
  beyond the reference platforms we test. However, since we do not systematically test
  them we cannot vouch for them. Problems encountered when running Eclipse on a
  non-reference platform that cannot be recreated on any reference platform will
  be given lower priority than problems with running Eclipse on a reference platform.</p>
<p>Eclipse 4.4 is tested and validated on the following reference platforms
  (this list is updated over the course of the release cycle):</p>

<center>
	<table class="platforms">
		<tr>
			<th>Operating System</th>
			<th>Version</th>
			<th>Hardware</th>
			<th>JRE</th>
			<th>Windowing System</th>
		</tr>
		<!-- ************ WINDOWS ************** -->
		<tr class="c0">
			<td rowspan="2">Windows</td>
			<td rowspan="2">7<br/>
			8
			</td>
			<td rowspan="1">x86 32-bit</td>
			<td rowspan="2">
				Oracle Java 8u5<br/>
				Oracle Java 7u55<br/>
				IBM Java 7 SR 7<br/>
				IBM Java 6 SR16
			</td>
			<td rowspan="2">Win32</td>
		</tr>
		<tr>
			<td rowspan="1">x86 64-bit</td>
		</tr>
		<!-- ************ RHEL ************** -->
		<tr class="c1">
			<td rowspan="4">Red Hat Enterprise Linux</td>
			<td rowspan="4">6</td>
			<td rowspan="1">x86 32-bit</td>
			<td rowspan="2">
				Oracle Java 8u5<br/>
			    Oracle Java 7u55<br/>
				IBM Java 7 SR 7<br/>
				IBM Java 6 SR16<br/>
				Open JDK 7u40
			</td>
			<td rowspan="4">GTK+ 2</td>
		</tr>
		<tr class="c1">
			<td rowspan="1">x86 64-bit</td>
		</tr>
		<tr class="c1">
			<td rowspan="1">Power 32-bit</td>
			<td rowspan="2">IBM Java 7 SR7<br/>
			IBM Java 6 SR16</td>
		</tr>
		<tr class="c1">
			<td rowspan="1">Power 64-bit</td>
		</tr>
		<!-- ************ SLES ************** -->
		<tr class="c0">
			<td rowspan="4">SUSE Linux Enterprise Server</td>
			<td rowspan="4">11</td>
			<td rowspan="1">x86 32-bit</td>
			<td rowspan="2">
				Oracle Java 8u5<br/>
				Oracle Java 7u55<br/>
				IBM Java 7 SR7<br/>
				IBM Java 6 SR16
			</td>
			<td rowspan="4">GTK+ 2</td>
		</tr>
		<tr class="c0">
			<td rowspan="1">x86 64-bit</td>
		</tr>
		<tr class="c0">
			<td rowspan="1">Power 32-bit</td>
			<td rowspan="2">IBM Java 7 SR7<br/>
			IBM Java 6 SR16</td>
		</tr>
		<tr class="c0">
			<td rowspan="1">Power 64-bit</td>
		</tr>
		<!-- ************ Ubuntu ************** -->
		<tr class="c1">
			<td rowspan="4">Ubuntu Long Term Support</td>
			<td rowspan="2">12.04</td>
			<td rowspan="1">x86 32-bit</td>
			<td rowspan="2">
				Oracle Java 8u5<br/>
				Oracle Java 7u55<br/>
				IBM Java 7 SR7<br/>
				IBM Java 6 SR16
			</td>
			<td rowspan="2">GTK+ 3</td>
		</tr>
		<tr class="c1">
			<td rowspan="1">x86 64-bit</td>
		</tr>
		<tr class="c1">
			<td rowspan="2">14.04</td>
			<td rowspan="1">x86 32-bit</td>
			<td rowspan="2">
				Oracle Java 8u5<br/>
				Oracle Java 7u55<br/>
				IBM Java 7 SR7<br/>
				IBM Java 6 SR16
			</td>
			<td rowspan="2">GTK+ 2</td>
		</tr>
		<tr class="c1">
			<td rowspan="1">x86 64-bit</td>
		</tr>
		<!-- ************ Solaris ************** -->
		<tr class="c0">
			<td rowspan="2">Oracle Solaris</td>
			<td rowspan="2">11</td>
			<td rowspan="1">x86 32-bit</td>
			<td rowspan="2">
			Oracle Java 8u5<br/>
			Oracle Java 7u55</td>
			<td rowspan="2">GTK+ 2</td>
		</tr>
		<tr class="c0">
			<td rowspan="1">SPARC 32-bit</td>
		</tr>
		<!-- ************ HPUX ************** -->
		<tr class="c1">
			<td rowspan="1">HP-UX</td>
			<td rowspan="1">11i v3</td>
			<td rowspan="1">ia64 64-bit</td>
			<td rowspan="1">HP-UX Java 6u20</td>
			<td rowspan="1">GTK+ 2</td>
		</tr>
		<!-- ************ AIX ************** -->
		<tr class="c0">
			<td rowspan="2">IBM AIX</td>
			<td rowspan="2">7.1</td>
			<td rowspan="1">Power 32-bit</td>
			<td rowspan="2">IBM Java 7 SR7<br/>
			IBM Java 6 SR16</td>
			<td rowspan="2">GTK+ 2</td>
		</tr>
		<tr class="c0">
			<td rowspan="1">Power 64-bit</td>
		</tr>
		
		<!-- ************ Mac ************** -->
		<tr class="c1">
			<td rowspan="2">Apple Mac OS X</td>
			<td rowspan="2">10.9</td>
			<td rowspan="1">Universal 32-bit</td>
			<td rowspan="2">
				Oracle Java 8u5<br/>
				Oracle Java 7u55</td>
			<td rowspan="2">Cocoa</td>
		</tr>
		<tr class="c1">
			<td rowspan="1">x86 64-bit</td>
		</tr>
	</table>
 </center>

<p>As stated above, <i>we expect that Eclipse works fine on other current
  Java VM and OS versions but we cannot flag these as reference platforms without
  significant community support for testing them.</i></p>

<p>The Eclipse SDK is designed as the basis for internationalized products. The
  user interface elements provided by the Eclipse SDK components, including dialogs
  and error messages, are externalized. The English strings are provided as the
  default resource bundles.</p>
<p>Latin-1, DBCS, and BiDi locales are supported by the Eclipse SDK on all reference platforms.</p>
<p>The Eclipse SDK supports GB 18030 (level 1), the Chinese code page standard,
  on Windows, Linux and the Macintosh.</p>
<p>German and Japanese locales are tested.</p>

<h2>2. <a name="Compatibility"></a>Compatibility with Previous Releases</h2>
<h3>Compatibility of Release 4.4 with 4.3</h3>
<p>Eclipse 4.4 is compatible with Eclipse 4.3 (and all earlier 3.x versions).</p>

<p><strong>API Contract Compatibility:</strong> Eclipse SDK 4.4 is upwards
  contract-compatible with Eclipse SDK 4.3 except in those areas noted in the
  <a href="http://www.eclipse.org/eclipse/development/porting/eclipse_4_4_porting_guide.html" target="_top">
    <em>Eclipse 4.4 Plug-in Migration Guide</em>
  </a>. Programs that use affected APIs and extension points will need to be ported
  to Eclipse SDK 4.4 APIs. Downward contract compatibility
  is not supported. There is no guarantee that compliance with Eclipse SDK 4.4
  APIs would ensure compliance with Eclipse SDK 4.3 APIs. Refer to
  <a href="http://wiki.eclipse.org/index.php/Evolving_Java-based_APIs">
    <em>Evolving Java-based APIs</em>
  </a> for a discussion of the kinds of API changes that maintain contract compatibility.</p>
  
<p><strong>Binary (plug-in) Compatibility:</strong> Eclipse SDK 4.4 is upwards
  binary-compatible with Eclipse SDK 4.3 except in those areas noted in the
  <a href="http://www.eclipse.org/eclipse/development/porting/eclipse_4_4_porting_guide.html" target="_top">
    <em>Eclipse 4.4 Plug-in Migration Guide</em>
  </a>. Downward plug-in compatibility is not supported. Plug-ins for Eclipse SDK
  4.4 will not be usable in Eclipse SDK 4.3. Refer to
  <a href="http://wiki.eclipse.org/index.php/Evolving_Java-based_APIs">
    <em>Evolving Java-based APIs</em>
  </a> for a discussion of the kinds of API changes that maintain binary compatibility.</p>
  
<p><strong>Source Compatibility:</strong> Eclipse SDK 4.4 is upwards source-compatible
  with Eclipse SDK 4.3 except in the areas noted in the
  <a href="http://www.eclipse.org/eclipse/development/porting/eclipse_4_4_porting_guide.html" target="_top">
    <em>Eclipse 4.4 Plug-in Migration Guide</em>
  </a>. This means that source files written
  to use Eclipse SDK 4.3 APIs might successfully compile and run against Eclipse
  SDK 4.4 APIs, although this is not guaranteed. Downward source compatibility
  is not supported. If source files use new Eclipse SDK APIs, they will not be
  usable with an earlier version of the Eclipse SDK. </p>
  
<p><strong>Workspace Compatibility:</strong> Eclipse SDK 4.4 is upwards
  workspace-compatible with earlier 3.x and 4.x versions of the Eclipse SDK unless noted.
  This means that workspaces and projects created with Eclipse SDK 4.3, 4.2, .. 3.0 can be successfully
  opened by Eclipse SDK 4.4 and upgraded to a 4.4 workspace. This includes both
  hidden metadata, which is localized to a particular workspace, as well as metadata
  files found within a workspace project (e.g., the .project file), which may
  propagate between workspaces via file copying or team repositories. Individual
  plug-ins developed for Eclipse SDK 4.4 should provide similar upwards compatibility
  for their hidden and visible workspace metadata created by earlier versions;
  4.4 plug-in developers are responsible for ensuring that their plug-ins recognize
  metadata from earlier versions and process it appropriately. User
  interface session state may be discarded when a workspace is upgraded. Downward
  workspace compatibility is not supported. A workspace created (or opened) by
  a product based on Eclipse 4.4 will be unusable with a product based on an earlier
  version of Eclipse. Visible metadata files created (or overwritten) by Eclipse
  4.4 will generally be unusable with earlier versions of Eclipse. </p>
  
<p><strong>Non-compliant usage of API's</strong>: All non-API methods and classes,
  and certainly everything in a package with &quot;internal&quot; in its name or
  x-internal in the bundle manifest entry,
  are considered implementation details which may vary between operating environment
  and are subject to change without notice. Client plug-ins that directly depend
  on anything other than what is specified in the Eclipse SDK API are inherently
  unsupportable and receive no guarantees about compatibility within a single
  release much less with earlier releases. Refer to
  <a href="https://www.eclipse.org/articles/Article-API-Use/index.html">
    <em>How to Use the Eclipse API</em>
  </a> for information about how to write compliant plug-ins. </p>

<h2>3. <a name="KnownIssues"></a> Known Issues</h2>
<blockquote>
  <a href="#I-General">
  3.1 General problems</a><br/>
  &nbsp;&nbsp;&nbsp;&nbsp; <a href="#I-General-Startup">3.1.1 Startup</a><br/>
  &nbsp;&nbsp;&nbsp;&nbsp; <a href="#I-General-GCJ">3.1.2 GCJ</a><br/>
  &nbsp;&nbsp;&nbsp;&nbsp; <a href="#I-General-64bitJava">3.1.3 64-bit Java HotSpot(TM) VM</a><br/>
  <a href="#I-Platform">3.2 Platform</a><br/>
  &nbsp;&nbsp;&nbsp;&nbsp; <a href="#I-Platform-Core">3.2.1 Core</a><br/>

  &nbsp;&nbsp;&nbsp;&nbsp; <a href="#I-Platform-Ant">3.2.2 Ant</a><br/>
  &nbsp;&nbsp;&nbsp;&nbsp; <a href="#I-Platform-User-Assistance">3.2.3 User Assistance</a><br/>
  &nbsp;&nbsp;&nbsp;&nbsp; <a href="#I-Platform-UI">3.2.4 UI</a><br/>
  &nbsp;&nbsp;&nbsp;&nbsp; <a href="#I-Platform-Text">3.2.5 Text</a><br/>
  &nbsp;&nbsp;&nbsp;&nbsp; <a href="#I-Platform-SWT">3.2.6 SWT</a><br/>

  &nbsp;&nbsp;&nbsp;&nbsp; <a href="#I-Platform-Team-CVS">3.2.7 Team and CVS</a><br/>
  &nbsp;&nbsp;&nbsp;&nbsp; <a href="#I-Platform-Install-Update">3.2.8  Install/Update</a><br/>
  &nbsp;&nbsp;&nbsp;&nbsp; <a href="#I-Platform-Debug">3.2.9 Debug</a><br/>
  &nbsp;&nbsp;&nbsp;&nbsp; <a href="#I-Platform-Compare">3.2.10 Compare</a><br/>
  <a href="#I-JDT">
  3.3 Java development tools (JDT)</a><br/>
  <a href="#I-PDE">
  3.4 Plug-in Development Environment (PDE)</a><br/>

</blockquote>
<p>Note: Bug numbers refer to the Eclipse project bug database at <a href="http://dev.eclipse.org/bugs/">http://bugs.eclipse.org/bugs/</a></p>

<h3>3.1 <a name="I-General">General problems</a></h3>

<h3>3.1.1 <a name="I-General-Startup">General - Startup</a></h3>
<h4>Installation/Configuration issues that can cause Eclipse to fail start</h4>
<p>Here are some common problems that can cause Eclipse not to start:</p>
<ul>
  <li>As shown <a href="#TargetOperatingEnvironments">above</a>, Eclipse 4.4 requires 
    at least a Java SE 6. Perhaps an older version of the VM is being found in 
    your path. To explicitly specify which VM to run with, use the Eclipse <tt>-vm</tt> 
    command-line argument. (See also the <a href="#RunningEclipse">Running Eclipse</a> 
    section below.)</li>
  <li>
    Running Eclipse on Gentoo Linux may result in the following error message:
    <div style="margin-left: 40px;">
<tt>* run-java-tool is not available for sun-jdk-1.6 on i686<br/>* IMPORTANT: some Java tools are not available on some VMs on some architectures</tt>
    </div>

If this occurs, start Eclipse by specifying a -vm argument, either
specify the path to a java vm or use: <tt>eclipse -vm `java-config</tt>
--java` (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=176021">176021</a>)</li>
<li>Eclipse must be installed to a clean directory and not installed over top of
a previous installation. If you have done this then please re-install to a new
directory. If your workspace is in a child directory of your old installation
directory, then see the instructions below on "<a href="#upgrading">Upgrading Workspace from a
Previous Release"</a>.</li>

<li>Java sometimes has difficulty detecting whether a file system is writable. In
particular, the method java.io.File.canWrite() appears to return true in
unexpected cases (e.g., using Windows drive sharing where the share is a
read-only Samba drive). The Eclipse runtime generally needs a writable
configuration area and as a result of this problem, may erroneously detect the
current configuration location as writable. The net result is that Eclipse will
fail to start and depending on the circumstances, may fail to write a log file
with any details. To work around this, we suggest users experiencing this
problem set their configuration area explicitly using the <tt>-configuration</tt> command
line argument. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=67719">67719</a>)</li>
</ul>

<h4><b>Invalid characters in install directory prevents Eclipse from starting</b></h4>
<p>Eclipse will fail to launch if installed in a directory whose path
contains certain invalid characters, including :%#&lt;&gt;&quot;!. The
workaround is to install Eclipse in a directory whose path does not contain
invalid characters. (bugs <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=3109">3109</a>
and <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=17281">17281</a>)</p>

<h4>Hanging during class loading when out of permanent generation memory</h4>
<p>
The Oracle JVM may hang indefinitely during class loading if it runs out of permanent
generation memory.  This will cause CPU usage to stay at 100% until the process
is ended.  See the section <a href="#RunningEclipse">Running Eclipse</a> for details
on addressing this VM problem.
</p>

<h3>3.1.2 <a name="I-General-GCJ">General - GCJ</a></h3>
<p>GCJ is an effort by the GCC team to provide an open source Java compiler and
runtime environment to interpret Java bytecode. Unfortunately, the GCJ runtime
environment is not an environment that is often tested on by Eclipse developers.</p>

<p>The most common problems surrounding GCJ are:</p>
<ul>
<li>Eclipse does not start at all</li>
<li>Eclipse throws a 'java.lang.ClassNotFoundException: org.eclipse.core.runtime.Plugin' that can be found in the logs (located in
workspace/.metadata/.log)</li>	
</ul>

<p>The workspace's log file is a good place to check to identify whether GCJ is
being used or not. Every Eclipse log session is prepended with
information about the runtime environment that was used to run Eclipse. The log
may include something like the following:</p>

<code>java.fullversion=GNU libgcj 4.2.1 (Debian 4.2.1-5)</code>

<p>If Eclipse does start, one can check which runtime environment is being used to
run Eclipse by going to <b>Help &gt; About Eclipse SDK &gt; Installation Details &gt; Configuration</b>. The
<b>About</b> dialog itself can also provide other information, the build identifier
can be of particular interest as it is tagged by some distributions. This allows the
user to identify whether Eclipse was downloaded through the distribution's
package management system or directly from the eclipse.org web site.</p>

Eg: <code>Build id: M20070212-1330 (Ubuntu version: 3.2.2-0ubuntu3)</code>

<p>The two most common workarounds are:</p><ul>
<li>download the Eclipse binary from eclipse.org directly</li>
<li>run Eclipse using an alternate Java runtime environment</li></ul>

<p>To download Eclipse, try one of the links below:</p><ul>
<li><a href="http://www.eclipse.org/downloads/">http://www.eclipse.org/downloads/</a></li>
<li><a href="http://download.eclipse.org/eclipse/downloads/">http://download.eclipse.org/eclipse/downloads/</a></li></ul>

It is imperative that 64-bit builds are downloaded and used if a 64-bit Java
runtime environment has been installed. Below are two sample tarball names of
version 4.4 of the Eclipse SDK packaged for 32-bit and 64-bit processors.

<pre>eclipse-SDK-4.4-linux-gtk.tar.gz (32-bit)
eclipse-SDK-4.4-linux-gtk-x86_64.tar.gz (64-bit)</pre>

<p>To run Eclipse with an alternate Java runtime environment, the path to the Java
virtual machine's binary must be identified. With an Eclipse installation from
the distribution, altering the $PATH variable to include the path to the
alternate Java runtime environment is often not enough as the Eclipse that
Linux distributions package often performs a scan internally to pick up GCJ by
itself whilst ignoring what's on the $PATH. An example of the terminal's output
is shown below:</p>

<code>searching for compatible vm...<br/>
  testing /usr/lib/jvm/java-7-icedtea...not found<br/>
  testing /usr/lib/jvm/java-gcj...found</code>

<p>Once the path to the virtual machine's binary has been identified, try running
Eclipse with the following command:</p>

<code>./eclipse -vm /path/to/jre/bin/java</code>

<p>For an actual example, it might look something like the following:</p>

<code>./eclipse -vm /usr/lib/jvm/sun-java-6/bin/java<br/>
./eclipse -vm /opt/sun-jdk-********/bin/java</code>

<p>If this seems to solve the problem, it is likely that the problem really was
related to the use of GCJ as the Java runtime for running Eclipse. The
eclipse.ini file located within Eclipse's folder can be altered to
automatically pass this argument to Eclipse at startup. An example of its
content is presented below:</p>

<code>-showsplash<br/>
org.eclipse.platform<br/>
--launcher.XXMaxPermSize<br/>
256m<br/>
-vm<br/>
/opt/sun-jdk-********/bin/java<br/>
-vmargs<br/>
-Xms40m<br/>
-Xmx512m</code>

<p>Note that every argument must be on its own line. More information about the
eclipse.ini file can be found at <a href="http://wiki.eclipse.org/Eclipse.ini">http://wiki.eclipse.org/Eclipse.ini</a>.</p>

<p>If problems persists after downloading an installation of Eclipse from
eclipse.org and using a supported Java runtime environment (a list of which may be found <a href="#TargetOperatingEnvironments">above</a>), 
you can seek further assistance through the <a href="http://www.eclipse.org/newsgroups/">newsgroups</a>, 
the IRC <a href="irc://irc.freenode.net/#eclipse">channel</a>, 
and/or <a href="https://bugs.eclipse.org/bugs/">bugzilla</a>.
</p>

<h3>3.1.3 <a name="I-General-64bitJava">General - 64-bit Java HotSpot(TM) VM</a></h3>
<p>
There is a known issue with the Java HotSpot(TM) 1.6.0 VM compiler which causes eclipse to 
crash (see Sun bug <a href="http://bugs.sun.com/bugdatabase/view_bug.do?bug_id=6614100">http://bugs.sun.com/bugdatabase/view_bug.do?bug_id=6614100</a>,
and Eclipse bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=214092">214092</a>).
The crash usually occurs within a VM CompilerThread when attempting to compile the method org.eclipse.core.internal.dtree.DataTreeNode.forwardDeltaWith.
</p>
<p>
This problem has been addressed in Sun Java 6 update 11, so the simplest resolution is
to obtain the latest JRE release for your platform.
To work around the issue you can exclude the method org.eclipse.core.internal.dtree.DataTreeNode.forwardDeltaWith from being compiled with the following
VM argument:
</p>

<code>
-XX:CompileCommand=exclude,org/eclipse/core/internal/dtree/DataTreeNode,forwardDeltaWith
</code>

<p>
This VM argument can be placed in the eclipse.ini file after the -vmargs line like the following:
</p>

<code>
-startup<br/>
plugins/org.eclipse.equinox.launcher.win32.win32.x86_1.0.200.v20090306-1900<br/>
--launcher.library<br/>
plugins/org.eclipse.equinox.launcher_1.0.200.v20090429-1630.jar<br/>
-showsplash<br/>
org.eclipse.platform<br/>
--launcher.XXMaxPermSize<br/>
256m<br/>
-vmargs<br/>
-XX:CompileCommand=exclude,org/eclipse/core/internal/dtree/DataTreeNode,forwardDeltaWith<br/>
-Xms40m<br/>
-Xmx256m<br/>
</code>

<p>
There have been reports of other classes that cause the compiler to crash.  If all else fails you can 
disable the compiler with the VM arg &quot;-Xint&quot;. 
</p>

<h3>3.2 <a name="I-Platform">Platform</a></h3>

<h3>3.2.1 <a name="I-Platform-Core">Platform - Core</a></h3>

<h4>Installing plug-ins by unzipping them into the plugins directory</h4>
<p>New plug-ins can be installed into the system by unzipping them into the plugins
directory. However this is not recommended, and the dropins directory should be
used for this purpose instead. Note that unzipping a different
version of a plug-in that is already installed will have no effect. To change the version
of a plug-in installed in your system, you need to either perform an update, or install
a feature patch.</p>

<h4>No branding with old config.ini</h4>
<p>If you have an old config.ini file and use it with a new Eclipse build, you
may not get the correct product branding. This is because the id of the standard
Eclipse product changed. Users in shared install scenarios may end up in this
situation as previous builds of Eclipse automatically generated config.ini files
in some cases. The work around is either to delete the local config.ini or
update the eclipse.product line to read eclipse.product=org.eclipse.platform.ide.</p>

<h4>Problems with classloaders in created threads</h4>
<p>There is a known issue with trying to load classes from a newly-created
thread using a class loader different from the plug-in class loader. The result
will be a <code>ClassNotFoundException</code>. As a workaround, do the
following:</p>
<ol>
  <li>Create a thread in which to run your code.</li>
  <li>Send yourThread.setContextClassLoader(yourClassLoader); // you can find
    your classloader by grabbing a class it loaded (YourPluginClass.class.getClassLoader())</li>
  <li>Run your code in the newly created thread.</li>
</ol>
<p>If you set the context class loader for the current thread, you are
competing with other users of the thread (all of Eclipse), so the results will
be unpredictable. However, there should be no problem in practice provided you
reset the context class loader back to its original value when your use in the
current thread is&nbsp;complete. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=8907">8907</a>)</p>

<h4>Deadlock creating executable extension in Plugin.startup</h4>
<p>If <code>Plugin.startup</code> code is too complex and performs tasks such
as creating an executable extension, a deadlock situation can be created. Only
simple bookkeeping tasks should be performed in <code>Plugin.startup</code>
code. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=5875">5875</a>)</p>
<h4>Potential Problems Converting Plug-in Manifests</h4>
<p>If your plug-in ships with a plug-in manifest and not an OSGi bundle manifest,
is shipped as a JAR file, and contains a nested JAR file then there may be
problems in the automatic generation of the bundle manifest file. The packages
defined in the nested JAR may not be exported correctly in the <tt>Export-packages</tt>

bundle manifest header. To work around this you should ship your plug-in with a
bundle manifest. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=97689">97689</a>)</p>
<h4>Location for Debug Options File on Mac OS</h4>
<p>If you are running in debug mode on Mac OS, the default location for the
.options file is inside the application bundle in the Eclipse.app/Contents/MacOS
directory (like the eclipse.ini). (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=88782">88782</a>)</p>

<h4>Issues with JNI that use FindClass</h4>
<p>
There may be issues when using a JNI implementation that uses FindClass 
in a function where the JNIEnv pointer is not available, such as in a C 
callback (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=125250">125250</a>).  The reason is that FindClass, in this case, uses the application
class loader to find the class.
If the desired class is in the classpath used for the application classloader
(e.g. defined by the VM argument -cp &lt;classpath&gt;), as it would typically be in 
a stand-alone application, there is no problem.  However, under
Eclipse, the application classloader does not have access to classes 
contained in plug-ins.  Eclipse uses its own class loader to find classes 
contained in plug-ins.
</p>
<p>
The proper plug-in class loader is used by FindClass in JNI functions which are 
passed the JNIEnv pointer, but not when you have to use AttachCurrentThread to get the 
JNIEnv pointer.  In this case the application classloader is used.
</p>
<p>
For example, the following will fail because AttachCurrentThread is used to 
get the JNIEnv pointer:</p>
<pre>
static JavaVM* jvm;  // Global variable

void myCallback(void) {
    JNIEnv* env;
    jvm-&gt;AttachCurrentThread((void**)&amp;env, NULL);
    // Fails if some/class is not in the application classloader:
    jclass cls = env-&gt;FindClass(&quot;some/class&quot;);
    jmethodID methodID = env-&gt;GetMethodID(cls, &quot;methodName&quot;,
      &quot;(Ljava/lang/String;)V or whatever signature&quot;);
    env-&gt;CallVoidMethod(callback, methodID, ...);
    jvm-&gt;DetachCurrentThread();
  }
}
</pre>
<p>
A solution is to cache the method ID, for example:
</p>
<pre>
static jmethodID mid;  // Global variable

JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM *vm, void *reserved) {
...
  // Store the JavaVM pointer
    jvm = vm;

  // Find the class and store the method ID
  // Will use the class loader that loaded the JNI library
    jclass cls = env-&gt;FindClass(className&quot;some/class&quot;);
    if(!cls) goto ERR;

    mid = env-&gt;GetMethodID(cls, &quot;methodName&quot;,
      &quot;(Ljava/lang/String;)V or whatever signature&quot;);
    if(!mid) goto ERR;
...
}

void myCallback(void) {
    JNIEnv* env;
    jvm-&gt;AttachCurrentThread((void**)&amp;env, NULL);
    env-&gt;CallVoidMethod(callback, mid, ...);
     // Handle error ...
    jvm-&gt;DetachCurrentThread();
  }
}
</pre>

<h3>3.2.2 <a name="I-Platform-Ant">Platform - Ant</a></h3>
<h4> Custom Ant tasks and Ant types must be separate from plug-in library JARs</h4>
<p>Including the class files for custom Ant tasks or Ant types in the regular
code JAR for your plug-in causes problems. These class files must be provided in
a separate JAR that is contributed to the <code>org.eclipse.ant.core.antTasks</code>
or <code>antTypes</code> extension point (and not declared as a library in the
plug-in's manifest). This ensures that the Ant tasks and types are loaded by the
special Ant class loader and not by a plug-in classloader. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=34466">34466</a>).</p>

<h4> Concurrent Ant builds not supported</h4>
<p>Eclipse can run Ant in the same JVM as the rest of Eclipse. Several aspects
of Ant and its use of global Java resources (such as System.out and System.err),
make it unsafe to run more than one Ant build concurrently in the same JVM. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=24129">24129</a>).</p>

<h4>XDoclet support from within Eclipse</h4>
<p>Since there are differences when running Ant from the commandline and within Eclipse, some extra steps may be needed to have XDoclet support function correctly within Eclipse. Problems may occur creating XDoclet subtasks. The workarounds and full discussion can be found in bug report. (bug
<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=37070">37070</a>)</p>

<h4>Ant Editor code completion based on Ant 1.6.x</h4>
<p>Code completion provided by the Ant editor does not respect the user-specified version of org.eclipse.ant.core plug-in or ANT_HOME. 
Code completion proposals are mostly based on Ant 1.6.x with some updates to Ant 1.8.3 (bug
<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=193046">bug 193046</a>)</p>

<h4> Setting build loggers not supported when debugging Ant builds</h4>
<p>When debugging Ant builds within Eclipse, setting <code>-logger</code> as a program argument will be ignored.</p>

<h4>Renaming an External Tool builder set to run during auto-build will cause errors</h4>
<p>If you rename an existing external tool builder that is configured to run during auto-builds, you will get the following error:
 Errors during build.
      Errors running builder "Integrated External Tool Builder" on project
      &lt;PROJECT_NAME&gt;.
      The builder launch configuration could not be found.
The workaround is to first disable the builder for auto-builds and then rename the builder.
(bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=118294">118294</a>)</p>

<h4>Slow typing/saving of the Ant editor with imports that define numerous macrodefs</h4>
<p>The Ant editor is slow on saving with buildfiles that have &lt;import&gt; declarations of buildfiles that have numerous &lt;macrodef&gt;s.
See bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=125117">125117</a> for a possible workaround</p>

<h4>Failure to run Ant builds on non-Windows platforms if Eclipse installed in location with spaces in the path</h4>
<p>Due to a bug in Ant 1.7.0, Ant builds will fail with an IllegalArgumentException if the Eclipse installation is in a location with spaces in the path.
Embedded usage of Ant builds, such as plug-in export will also fail.
See bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=187993">187993</a> for possible workarounds</p>

<h4>Ant 1.8.x reports missing libraries as build failures</h4>
<p>
In Ant 1.8.x, if you try to use a task that requires additional libraries and you do not have the libraries on the Ant classpath, the build will now properly report as failed. 
In previous versions of Ant, the build would still report that it had suceeded even though it actually failed to run any of the tasks from additional bundles. See <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=344518">bug 344518</a>.
</p>
<p>
For more information on tasks that require additional bundles please refer to the <a href="http://www.apache.org/dist/ant/RELEASE-NOTES-apache-ant-1.8.2.html">Ant 1.8.2 release notes</a>
and the <a href="http://ant.apache.org/manual/install.html#optionalTasks">Optional Tasks</a> section in the Ant manual. 
</p>

<h3>3.2.3 <a name="I-Platform-User-Assistance">Platform - User Assistance</a></h3>
<h4>Welcome page not displayed properly (Linux/Unix)</h4>
<p>The default Welcome implementation is HTML-based and requires a supported browser
in order to work. If no supported browser can be found, Welcome falls back to its
Forms-based implementation, which has a different (simpler) appearance. Consult the
<a href="http://www.eclipse.org/swt/faq.php#browserplatforms">SWT FAQ</a> for supported
browsers and setting up your browser to work with eclipse.
</p>

<h4>Help browser tool bar buttons do not work for some documents</h4>
<p>The Help browser's Print, Synchronize, and Bookmark buttons do not work for
pages that are not actually installed with the product. However, you can always
use the print command in the browser's context menu to print the page you're
reading. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=44216">44216</a>)</p>
<h4> Help documents not displayed
in a browser or very slow document loading (Windows only)</h4>

If your LAN settings are not properly configured for local host access, your
Help browser might open to a blank page or display an HTTP error instead of a
help page, or you may experience long delays when loading help documents. Your
system administrator can configure your LAN settings so that help documents can
be accessed from the local help server.
<blockquote>
  <ol>
    <li>In the Control Panel, open <b>Internet Options</b>, select the <b>Connections</b>
      tab and choose <b>LAN Settings</b>.</li>
    <li>If your host was configured to use DHCP for IP assignment, make sure
      that the &quot;Automatically detect settings&quot; check box is cleared.</li>
    <li>If you use a proxy server, ensure that the &quot;Bypass proxy server
      for local addresses&quot; is selected.</li>
    <li>In &quot;Advanced&quot; settings for proxies, add
      &quot;127.0.0.1;localhost&quot; to the &quot;Exceptions&quot; if these
      addresses are not listed.</li>
    <li>If you are using an automatic configuration script for proxy
      settings, and are not sure that the script is correct, clear the &quot;Use
      automatic configuration script&quot; check box.</li>

  </ol>
</blockquote>
<h4> Working disconnected from
the network (Windows only)</h4>
If you are experiencing problems when not connected to the network, you must
install the loopback adapter from the Windows installation CD. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=831">831</a>)
<h4> Using Internet Explorer in
offline mode (Windows only)</h4>

If you have been using Internet Explorer in Offline mode, when you access the
help system you will get a message indicating that the web page you requested is
not available offline or a blank page will display. Click <b>Connect</b> or
deselect &quot;Work Offline&quot; in the Internet Explorer &quot;File&quot; menu
to return the system behavior to normal.
<h4>Help topics not highlighted in High Contrast mode (Windows only)</h4>
<p>Windows High Contrast settings are not consistently picked up by Internet
Explorer when they are set from the Accessibility Options utility as opposed to
when they are set using the predefined schemes. On Windows XP, it is recommended
to set High Contrast as follows: Right click the desktop, chose properties,
select Windows Classic style from the Windows and buttons drop down on the
Appearance tab, and choose your scheme (for example High Contrast Black) from
Color Scheme drop down. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=28609">28609</a>)</p>

<h3>3.2.4 <a name="I-Platform-UI">Platform - UI</a></h3>
<h4>High contrast settings</h4>
<p>Eclipse was tested for High Contrast using 1152 x 864 resolution in Windows
XP High Contrast mode. You can select this mode by selecting Accessibility
Options &gt; Display &gt; Use High Contrast from the Windows XP Control Panel
menu.</p>

<h4> Dirty state not tracked properly for OLE documents (Windows only)</h4>
<p>The dirty state for an OLE document is not updated properly. This causes
Eclipse to prompt to save the contents of the editor when the document is
closed, even if the contents have already been saved. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=2564">2564</a>)</p>
<h4> OLE document crashes can cause Eclipse to also crash (Windows only)</h4>
<p>If an OLE document crashes, Eclipse can crash, or the workbench menus can
become inconsistent.</p>
<h4>Toolbars only containing contributed controls exhibit display errors on Mac/Linux</h4>
<p>
Currently there is no way on the Max or Linux platforms to define the <b>height</b> for controls contributed to
toolbars, nor will those platforms respect the size returned by the control's <code>computeSize</code> method. If you
encounter this issue there is currently no truly viable workaround.
(bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=183003">183003</a>)
</p>
<h4>Customizing menus and toolbars not working reliably</h4>
<p>
The Customize Perspective Dialog can still be used to turn on action sets in the Command Groups Availability tab,
but the items contained within the action sets are no longer displayed in the dialog.  The Toolbar Visibility
and Menu Visibility no longer display the correct information or icons, and will not work correctly.
(bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=378849">378849</a>)
</p>
<h4>Launching an inner eclipse can lead to PermGen errors</h4>
<p>
On some Oracle JVMs, launching an inner eclipse during plug-in development can lead to PermGen errors
for the inner eclipse.  The native launcher checks the JVM  and can add <i>-XX:MaxPermSize=256m</i>,
but PDE launches simply use java and don't go through the native launchers.  The workaround is to add
the appropriate JVM arg to your launch config or to the <i>Preferences &gt; Java &gt; Installed JREs</i>.
(bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=339763">339763</a>)
</p>
<h4>Capabilities and Activities don't affect the menus and toolbars</h4>
<p>
Capabilities used to hide GUI elements like menu entries work for commands and individual
actionSet entries, but Capabilities have not been fully implemented.
(bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=359778">359778</a>)
</p>

<h3>3.2.5 <a name="I-Platform-Text">Platform - Text</a></h3>
None.

<h3>3.2.6 <a name="I-Platform-SWT">Platform - SWT</a></h3>
<h4>Eclipse falls back to GTK+ 2 as default on Linux with GTK+ 3 version &gt; 3.8.x</h4>
<p>Linux builds come with GTK+ 3 support enabled by default on GTK+ 3 versions prior to 3.10, versions newer 
than that will fall back to GTK+ 2.x by default for maximum stability. The GTK+ 3 support can however, still be 
enabled by using either the environment variable <code>SWT_GTK3</code> or the launcher parameter 
<code>--launcher.GTK_version</code> (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=434619">434619</a>)</p>

<h4>Eclipse crashes randomly when using the Webkit based browser on RHEL 6.5</h4>
<p>The Webkit version (1.2.6) that is bundled with RHEL 6.5 crashes randomly while evaluating Javascript code. 
This has been fixed in more recent versions of Webkit which are not available on RHEL 6.5 yet. The workaround 
is to force Eclipse to use the Mozilla XULRunner based browser. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=428347">428347</a>)</p>

<h4>Table content is not rendered when running Eclipse under KDE with GTK+ 3 and "oxygen-gtk" theme</h4>
<p>Tables in Eclipse are not rendered on Linux distributions with the KDE desktop (such as KUbuntu) 
when using GTK+ 3 and the corresponding GTK+ 3 desktop theme "oxygen-gtk". The workaround is to switch 
to a different GTK+ 3 theme. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=432673">432673</a>)</p>

<h4>Eclipse plug-in based on the SWT Browser throws exception</h4>
<p>The SWT Browser widget uses a platform-specific web browser to render HTML.
The org.eclipse.swt.SWTError exception (&quot;No more handles&quot;) is thrown
on platforms that don't meet the requirements for running the Browser widget.
Supported platforms and prerequisites are listed on the SWT FAQ item <a href="http://www.eclipse.org/swt/faq.php#browserplatforms">
&quot;Which platforms support the SWT Browser?&quot;</a>.</p>

<h4>Opening File Dialog crashes eclipse (Vista only)</h4>
<p>On Vista, launching eclipse using <code>-vmargs -Xmx[any size]</code> can crash eclipse when the FileDialog is opened.
The workaround is to use the default heap size, i.e. do not use the <code>-Xmx</code> VM args.
(bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=188317">188317</a>)</p>

<h4>Crash while editing text (Windows XP with SP2 only)</h4>
<p>Some users who have installed Service Pack 2 on Windows XP have experienced
crashes while using editors in Eclipse.  The workaround is to place a working version 
of Windows\System32\USP10.DLL in the Eclipse startup directory or uninstall
Service Pack 2.
(bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=56390">56390</a>)</p>

<h4>Eclipse hangs when pasting from an unresponsive application (GTK only)</h4>
<p>If the application that is supplying the clipboard material is unresponsive,
the paste operation hangs Eclipse for several minutes. This situation can be
encountered when copying from an Eclipse target workbench, suspending the target
workbench at a breakpoint and pasting into the hosting Eclipse workbench. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=44915">44915</a>)</p>

<h4>IME conversion problem (Solaris GTK only)</h4>
<p>When typing Japanese text, the conversion to Kanji must be done one ideogram at
a time. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=226636">226636</a>)</p>

<h4>Eclipse won't start (Linux GTK PPC only)</h4>
<p>Eclipse fails to create a lock file with reason &quot;No locks available&quot;. 
To launch eclipse you must disable file locking using the osgi.locking property.
For example, you could launch eclipse as follows: <br/>
<code>eclipse -vmargs -Dosgi.locking=none</code>
</p>

<h4>Strings may be truncated or incorrectly wrapped on RHEL5 (Linux GTK only)</h4>
<p>
Strings on wrapping Controls may not appear correctly in some locales on RHEL5 as a result
of a bug in Pango version 1.14.x.  This problem can be fixed by upgrading the installed
Pango library to a version that is newer than 1.14.x. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=231951">231951</a>)
</p>

<h4>Block Selection functionality provided by StyledText is not BIDI aware</h4>
<p>
When the orientation of characters under the left and right edges of the block
selection rectangle are not the same, the actual selection ranges (in memory)
differ from the visual representation of the selection. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=277929">277929</a>)
</p>

<h4>Older versions of some Windows screen readers no longer work with Eclipse</h4>
<p>JAWS versions 8 and 9 and Window-Eyes version 6 no longer work well with Eclipse and other SWT applications.
Window-Eyes 6 will cause Eclipse to crash, and JAWS 8 and 9 can cause SWT applications to crash.
This happens because IAccessible2 support was added to SWT for Eclipse 3.7, but these older screen reader versions contain
partial implementations of IAccessible2 that do not follow the current IAccessible2 specification.</p>
<p>
The workaround for these cases is to specify Java property <code>org.eclipse.swt.accessibility.UseIA2</code> with value <code>false</code>,
which will instruct SWT to not attempt to use IA2 interfaces.  An easy way to set this property is to specify VM argument
<code>-Dorg.eclipse.swt.accessibility.UseIA2=false</code> when launching Eclipse or your SWT application. (bug
<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=313182">313182</a>)</p>

<h4>Drawing problems when using non-advanced graphics on recent GTK versions</h4>
<p>On modern Linux distributions with a GTK version greater than 2.18, clipping problems and pixel corruption 
can occur if the SWT client uses non-advanced GC calls. These problems seem to be caused by low-level bugs 
in the interactions between GDK and X.
</p>

<h4>Menus do not appear in Unity desktop menu bar</h4>
<p>On recent Ubuntu Linux distributions that feature the Unity desktop, the menus from the workbench 
will not appear in the top desktop menu bar. They will continue to appear in the shell.</p>

<h4>Slider controls do not draw on Ubuntu</h4>
<p>Sliders do not work on Linux distros with overlay scrollbars enabled  (such as
Ubuntu 12.04). A workaround for this is to disable the overlay scrollbars
(export LIBOVERLAY_SCROLLBAR=0 ) before launching Eclipse.</p>

<h4>BIDI Segments in Text controls</h4>
<p>BIDI Segments in Text controls only work on Windows and GTK.</p>

<h3>3.2.7 <a name="I-Platform-Team-CVS">Platform - Team - CVS</a></h3>
<p>The following are known problems with the CVS repository provider only, and
do not apply to other repository providers. Additional information on how to use
CVS from Eclipse can be found in the <a href="https://wiki.eclipse.org/CVS_FAQ">Eclipse
CVS FAQ</a>.</p>
<h4> CVS server compatibility</h4>
<p>The CVS plug-in parses messages returned from the CVS server. If the format
of these messages is not as expected, some of the plug-in's functionality may be
missing. The CVS plug-in is compatible with all stable 1.11.X builds of the CVS
server, and should be compatible with future releases in that stream unless text
message formats change (the last tested server was 1.11.22). As for the 1.12.X
feature releases of CVS, the Eclipse CVS client has been tested with builds up
to 1.12.13. However, future releases could easily break the Eclipse CVS client.
Basic functionality, such as Checkout, Commit, and Update, should always work,
but there may be problems with more advanced commands such as Synchronizing and
Browsing the repository.</p>
  
<h4>Connection cannot be found after initially missing</h4>
<p>If a connection initially fails due to a network problem, the connection may
continue to fail even when the network problem is fixed. In order to establish
the connection you must exit and restart Eclipse. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=9295">9295</a>)</p>

<h4>&quot;Received broken pipe signal&quot; error from server</h4>
<p>Eclipse sometimes performs multiple commands within a single connection to
the server. This may cause problems with CVS servers that are running server
scripts in response to certain commands. (bugs <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=23575">23575</a>
and <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=23581">23581</a>)</p>

<h4>&quot;Terminated with fatal signal 10&quot; error from server</h4>
<p>There is a bug in the CVS server related to some compression levels. If you
get this error, changing the compression level on the CVS preference page may
help. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=15724">15724</a>)</p>
<h4>&quot;Unknown response&quot; error using ext connection method</h4>
<p>There are a few situations that can result in an &quot;Unknown response&quot;
error messages when using the ext connection method. One situation involves
using an external communications client (e.g. rsh or ssh) that adds CRs to the
communications channel (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=21180">21180</a>).
Another involves Eclipse not properly reading the stderr output of the external
communications tool. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=11633">11633</a>)</p>

<h4>A disabled CVS capability may not be auto-enabled in existing workspaces</h4>
<p>New in 3.0 is the ability to disable capabilities and the CVS support in
Eclipse can be disabled. However, for backwards compatibility the CVS capability
is auto-enabled in existing workspaces that already contain CVS projects. The
auto-enabling function may not run if the team support plugin is not loaded at
startup. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=66977">66977</a>)</p>
<h4>Builder output files may appear as changed</h4>
<p>When folders containing build output are shared they may get improperly
marked as dirty when build output is generated.</p>

<h4>Enabling GNOME proxy support</h4>
<p>GNOME applications can make use of proxy settings defined in this environment.
If set, Eclipse will use it prior to proxy settings declared using env variables.
This feature is disabled by default, to enable it launch Eclipse with
<code>"-Dorg.eclipse.core.net.enableGnome"</code> switch. That is,</p>
<pre>eclipse -Dorg.eclipse.core.net.enableGnome</pre>

<h3>3.2.8 <a name="I-Platform-Install-Update">Platform - Install/Update</a></h3>
<h4>Manually installing features and plug-ins on a FAT file system (Windows only)</h4>

<p>When features and plug-ins are manually installed on top of an Eclipse-based
 product install located on a FAT file system that has already been run at least 
 once, the product must be explicitly restarted with -clean. That is,</p>
<pre>eclipse.exe -clean
</pre>

<h4>Connecting to untrusted sites using https</h4>
<p>You cannot install or update software from a site using https whose certificate
is not chained to a trusted root certificate in your local certificate store. This typically
means the server is using a self-signed certificate, or a certificate authenticated by
an unknown third party.</p>

<h4>Extension location is lost if the install path changes</h4>
<p>A previously configured extension location may be temporarily removed if the install is moved or mounted 
under a different path. This only happens when the link file that configures the 
extension location uses a relative path that points to a directory under the Eclipse 
install. On a second startup using the same install path, the extension location 
is added again (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=95403">95403</a>). <br/></p>

<h3>3.2.9 <a name="I-Platform-Debug">Platform - Debug</a></h3>
<p>None. (Known problems with the Java debugger appear below in the <a href="#I-JDT">JDT</a>
section.)</p>

<h3>3.2.10 <a name="I-Platform-Compare">Platform - Compare</a></h3>
<p>None.</p>

<h3>3.3 <a name="I-JDT">Java development tools (JDT)</a></h3>

<h4>Multiple regions formatting in a given source snippet</h4>
In version 3.4, the API method <code>org.eclipse.jdt.core.formatter.CodeFormatter.format(int, String, IRegion[], int, String)</code>
was added to allow the formatting of several regions in a source snippet with a single pass.<br/>
Even if specified, this method does not currently accept comments of the following kinds:
<ul>
<li><code>org.eclipse.jdt.core.formatter.CodeFormatter#K_SINGLE_LINE_COMMENT</code></li>
<li><code>org.eclipse.jdt.core.formatter.CodeFormatter#K_MULTI_LINE_COMMENT</code></li>
<li><code>org.eclipse.jdt.core.formatter.CodeFormatter#K_JAVA_DOC</code></li>
</ul>
This will be fixed in a future release (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=233967">233967</a>).

<h4>Searching for constant field references</h4>
<p>Search does not find references to constant fields inside binaries because
the Java Language Specification mandates that constant field values be inlined
in the class file's byte codes, leaving no trace of a field reference.&nbsp;(bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=12044">12044</a>)</p>

<h4> Cut, copy, paste not working
for linked resources in views showing Java elements</h4>
<p>The cut, copy, and paste actions do not work for linked files and folders
appearing in views that show Java elements, including the Package Explorer. The
workaround is to use these actions from the Navigator view instead. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=34568">34568</a>)</p>
<h4> Java working sets not
working correctly for elements from JRE system library container</h4>
<p>Applying a working set consisting entirely of elements from the JRE System
library container as a filter to the packages view might result in an empty
Package Explorer. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=30442">30442</a>)</p>

<h4>Breakpoints unreliable running Sun 1.6.0_14</h4>
Developers debugging applications on Sun's 1.6.0_14 
virtual machine should be aware that breakpoints are unreliable (i.e. do 
not always suspend execution). The problem occurs on Windows and Linux 
platforms. This is an issue with the VM and not with Eclipse.
The workaround is to use the <code>-XX:+UseParallelGC</code> VM option. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=279137">279137</a>).

<h4>Suspend on uncaught exception overrides exception breakpoint location filters</h4>
<p>Exception breakpoints can be configured with location filters (inclusive and 
  exclusive). When an unchecked exception is configured to <b>not</b> suspend 
  execution in a specific class, execution will still suspend when the user preference 
  to suspend on uncaught exceptions is on. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=66770">66770</a>)</p>

<h4>Running Java programs with non-Latin-1 characters in package or class names</h4>
You get a <code>java.lang.NoClassDefFoundError</code> when running Java
programs with non-Latin characters in the package or class names. The workaround
is to package the class files as a JAR file and run the program out of the JAR
and not from the file system directly. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=4181">4181</a>)

<h4>Cannot run or debug class in
a project with GB18030 characters in project name</h4>
<p>Most class libraries do not properly support the creation of a system
process (via <code>java.lang.Runtime.exec(...)</code>) when the specified
command line contains GB18030 characters. This limitation means the debugger
cannot launch applications when the command line it generates contains GB18030
characters. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=32206">32206</a>)</p>

<h4>Cannot detect installed JRE with GB18030 characters in path name</h4>
<p>Automatic JRE detection fails when the JRE is stored in a directory
containing GB18030 characters in its name. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=33844">33844</a>)</p>

<h4>Cannot generate Javadoc for packages with GB18030 characters in the name</h4>
<p>Most class libraries do not properly support the creation of a system
process (via <code>java.lang.Runtime.exec(...)</code>) when the specified
command line contains GB18030 characters. Since Javadoc is created using the
Javadoc executable provided with the JDK, generating Javadoc fails if the
package or class name contains GB18030 characters. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=32215">32215</a>)</p>

<h4> Unable to debug stack
overflows</h4>

<p>If a debug session suspends on a <code>java.lang.StackOverflowError</code>
exception (due to an exception breakpoint), the debugger may not be able to
retrieve any debug information from the target JVM. As well, the debugger may
not be able to reliably interact with the target JVM past this point. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=19217">19217</a>)</p>
<h4> Evaluation limitation</h4>
<p>The debugger uses threads in the target JVM to perform evaluations (both
explicit evaluations that the user requests, and implicit evaluations such as <code>toString()</code>
invocations in the <b>Variables</b> view). The Java Debug Interface (JDI)
requires that the thread in which an evaluation is performed be suspended by a
user event (that is, a breakpoint or step request). Evaluations cannot be
performed on threads suspended by the suspend action. As well, when a breakpoint
is configured to suspend the JVM rather than just the individual thread, the
threads which did not encounter the breakpoint are not in a valid state to
perform an evaluation. When an evaluation is attempted in a thread that is not
in a valid state to perform an evaluation, an error message will appear to the
effect of &quot;Thread must be suspended by step or breakpoint to perform method
invocation&quot;. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=34440">34440</a>)</p>

<h4> Missing debug attributes</h4>
The debugger requires that class files be compiled with debug attributes if
it is to be able to display line numbers and local variables. Quite often, class
libraries (for example, &quot;<code>rt.jar</code>&quot;) are compiled without
complete debug attributes, and thus local variables and method arguments for
those classes are not visible in the debugger.
<h4> Using Hot Code Replace</h4>
<p>Hot code replace is supported on JDK 1.4.x VMs, and IBM J9 VMs. The debugger
will attempt to replace all class files that change in the workspace as the user
edits and builds source code. However, hot code replace is limited to changes
that a particular virtual machine implementation supports. For example, changes
within existing methods may be supported, but the addition or removal of members
may not be.</p>
<h4> Scrapbook</h4>
Setting a breakpoint inside a scrapbook page is not supported.

<p>When a snippet is run in the scrapbook which directly or indirectly calls <code>System.exit(int)</code>,
the evaluation cannot be completed, and will result in a stack trace for a <code>com.sun.jdi.VMDisconnectedException</code>
being displayed in the scrapbook editor.</p>
<p>Terminating a scrapbook page while it is performing an evaluation results
in a <code>com.sun.jdi.VMDisconnectedException</code> being displayed in the
scrapbook editor.</p>
<h4> Debugging over slow
connections</h4>
A global Java debug preference specifies the debugger timeout, which is the
maximum amount of time the debugger waits for a response from the target VM
after making a request of that VM. Slow connections may require that this value
be increased. The timeout value can be edited from the <b>Java &gt; Debug </b>preference
page. Changing the timeout value only affects subsequently launched VM, not VMs
that are already running.

<h4> Updating of inspected values</h4>
When inspecting the result of an evaluated expression in the debugger, it is
important to note that the result displayed is the result of that expression at
the time it was evaluated. For example, when inspecting a simple integer counter
(primitive data type), the value displayed in the Expressions view is the value
when the expression was evaluated. As the counter is changed in the running
program, the inspected result will not change (since the view is not displaying
the value bound to a variable - it is displaying the value of an expression, and
the value of a primitive data type cannot change). However, if an expression
results in an object, fields of that object will be updated in the inspector as
they change in the running program (since the value bound to fields in an object
can change).
<h4> Stepping over native methods
that perform I/O</h4>
When the debugger steps over native methods that perform I/O to <code>System.out</code>
or <code>System.err</code>, the output may not appear immediately unless the
native performs a flush on the output buffer.
<h4> VM and process termination
running on IBM 1.3 JVM on Linux (Linux only)</h4>
Terminating a launch, debug target, or system process associated with a debug
target running on the IBM 1.3 JVM on the Linux platform does not work when the
associated debug target has a suspended thread. To remove such debug targets
from the debug UI, select <b>Terminate and Remove</b> from the debug view's
pop-up menu (or use the shortcut &quot;delete&quot; key). Associated system
processes in the OS may not be properly cleaned up. If a debug target has no
suspended threads, termination works properly. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=1631">1631</a>)

<h4>Java 6 and MacOS</h4>
Apple JavaSE-1.6 VMs only execute on 64-bit architectures but JDT will detect 1.6 VMs installed on 32-bit
architectures when a new workspace is started or when the user presses the &quot;Search...&quot; button
on the Installed JREs preference page. Error messages will appear in the log each time JDT attempts to
determine which execution environments a 1.6 VM is compatible with. JDT can be configured to ignore 1.6
JREs by removing them from the Installed JREs preference page.
(bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=262542">262542</a>)

<h4>Java Annotation Processing</h4>
<p>Some methods in the processing API are unimplemented when compiling within the IDE, and will
throw <code>UnsupportedOperationException</code>.</p>
<p>
Java 6 annotation processors are supported in the batch compiler and in the
IDE. By design, Java 6 processors are only executed during a build, not while
editing. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=188558">188558</a>)</p>
<p>
Java 5 annotation processors are supported in the IDE only. Java 5 processors
can be executed while editing, as well as during a build. Slow annotation
processors can cause a slowdown of the editing experience. If this occurs, you
may wish to turn off <b>Enable processing in editor</b> on the <b>Java Compiler &gt; Annotation Processing</b> properties
page of your Java project.</p>

<h4>Java indexing encounters problems when a folder is used both as a source and a class folder</h4>
<p>Java indexing encounters problems when a folder is used both as a source folder
in a project and as a class folder in another project. Hence, when this peculiar setup is
used, the Java Search might miss matches located in such a folder. To avoid this kind of problem, it is strongly advised to use different
folders for sources and binary classes. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=309903">309903</a>)</p>

<h3>3.4 <a name="I-PDE">Plug-in Development Environment (PDE)</a></h3>
<h4>Feature manifest editor does not preserve all comments</h4>

<p>When a non-source page of the feature manifest editor is used, PDE will convert
changes back into XML by regenerating the file. Although the overall content and
most of the comments are preserved, some comments may be lost. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=59502">59502</a>)</p>
<h4>PDE will not unzip source zips of some plug-ins</h4>
<p>In the plug-in import wizard, when you choose to import plug-ins as
&quot;projects with source folders&quot;, PDE will not unzip the source for the
org.apache.ant. This is
because the source ZIPs contains code that will not compile when unzipped as it
requires additional JARs that are not part of the SDK. To avoid the creation of
plug-in projects that won't compile, PDE will import these plug-ins as binary
and attach source, so you would still be able to read the source, you just won't
be able to modify it. Also, PDE will not unzip the source for the
org.eclipse.swt plug-ins. In this case, it is because, when shipped, the swt
code is spread across a plug-in and a fragment, and when unzipped, it will
require circular dependencies between the plug-in and fragment projects. These
circular dependencies are at minimum marked as warnings by the JDT compiler and
may result in unpredictable build behavior. Therefore, PDE always imports
org.eclipse.swt as binary with source attached. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=66314">66314</a>)</p>

<h4>Emacs key bindings do not
work in manifest editor fields</h4>
<p>Non-default key bindings currently do not work in fields on non-source
pages of the PDE manifest editors. (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=19482">19482</a>)</p>

<h4>Export of plug-in may silently drop classes</h4>
<p>When exporting a plug-in using the plug-in, feature or product wizards, some classes
might be dropped from the resulting archive if their fully qualified name is too long.
This typical path limitation can be worked around by creating the jar of the problematic
plug-in by using the Jar export wizard.
(bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=97150">97150</a>)</p>

<h4>Compilation errors when exporting projects not stored outside of the workspace</h4>
<p>When exporting multiple plug-ins and one is stored outside of the workspace,
compile errors occurs on export. To work around the problem, you can either export
the plug-ins one by one, or change their location.
(bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=98579">98579</a>)</p>

<h4>Headless build needs to be run from a fully qualified path</h4>
<p>When running a headless build using the scripts provided by pde build, the properties <code>builder</code> 
and <code>buildDirectory</code> must refer to a fully qualified path.
(bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=139554">139554</a>)
</p>

<h4>Importing in Eclipse application fails if plug-in exists in host workspace</h4>
<p>When running an Eclipse application (self-hosting) importing plug-ins will not
work correctly if the plug-in being imported exists in the host Eclipse's
workspace. This is because PDE modifies the target platform of the application
to point at the running plug-ins from the host (target weaving). This also
affects the PDE test suite.
(bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=294005">294005</a>)
</p>

<h4>Reusing a workspace after changing architectures silently breaks PDE models</h4>
<p>If a workspace is reused on a machine with a different architecture, the PDE
models used to build plug-ins may silently fail. To work around this problem,
delete the metadata in &lt;workspace&gt;/.metadata/.plugins/org.eclipse.pde.core.
(bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=350172">350172</a>)
</p>

<h4>Missing @since tag API Tools problems on interface fields containing @noreference tag</h4>
<p>The Eclipse platform 4.4 release will not allow the API Tools @noreference tag on interface fields. This was 
changed because all interface fields are constant fields that cannot support the @noreference restriction. The
tag was allowed in previous releases and this usage will now be considered an API change requiring a @since tag. 
It is recommended that you create an API Tools filter for the missing @since tag problem. This filter can be
removed as soon as the API baseline has been regenerated.
(bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=402393">402393</a>)
</p>

<h2>4. <a name="RunningEclipse">Running Eclipse</a></h2>
<p>After installing the Eclipse SDK in a directory, you can start the Workbench
by running the Eclipse executable included with the release (you also need a Java SE 6
JRE, not included with the Eclipse SDK). On Windows, the executable file is called <samp>eclipse.exe</samp>,
and is located in the <code>eclipse</code> sub-directory of the install. If
installed at <code>c:\eclipse-SDK-4.4-win32</code>, the executable is <code>c:\eclipse-SDK-4.4-win32\eclipse\eclipse.exe</code>.

<b>Note:</b> Set-up on most other operating environments is analogous. Special
instructions for Mac OS X are listed <a href="#macosx">below</a>.</p>

<h3>Allocating enough memory and solving OutOfMemoryErrors</h3>
<p>By default, Eclipse will allocate up to 512 megabytes of Java heap memory. This should
be ample for all typical development tasks. However, depending on the JRE
that you are running, the number of additional plug-ins you are using, and
the number of files you will be working with, you could conceivably have to increase this amount. 
Eclipse allows you to pass arguments directly to the Java VM using the
<code>-vmargs</code> command line argument, which must follow all other Eclipse specific arguments.
Thus, to increase the available heap memory, you would typically use:</p>
<blockquote>
  <p><code>eclipse -vmargs -Xmx&lt;memory size&gt;</code></p>
</blockquote>
<p>with the <code>&lt;memory size&gt;</code> value set to greater than
&quot;512M&quot; (512 megabytes -- the default). 
</p>
<p>
When using an Oracle VM below 1.8, you may also need to increase the size of the permanent
generation memory.  The default maximum is 64 megabytes, but more may
be needed depending on your plug-in configuration and use.  When the VM runs
out of permanent generation memory, it may crash or hang during class loading.
This failure is less common when using Oracle JRE version 1.5.0_07 or greater.
The maximum permanent generation size is increased using the -XX:MaxPermSize=&lt;memory size&gt; argument:</p>
<blockquote>
  <p><code>eclipse -vmargs -XX:MaxPermSize=&lt;memory size&gt;</code></p>
</blockquote>
<p>This argument may not be available for all VM versions and platforms; consult your VM documentation
for more details.
</p>
<p>
Note that setting memory sizes to be larger than the amount of available physical
memory on your machine will cause Java to &quot;thrash&quot; as it copies objects
back and forth to virtual memory, which will severely degrade your performance.
</p>
<h3>Selecting a workspace</h3>
<p>When the Workbench is launched, the first thing you see is a 
dialog that allows you to select where the workspace will be located. The
workspace is the directory where your work will be stored.
If you do not specify otherwise, Eclipse creates the workspace in your
user directory.
This workspace directory is used as the default content area for your projects
as well as for holding any required metadata. For shared or multi-workspace
installs you must explicitly specify the location for your workspace using the
dialog (or via the &quot;<code>-data</code>&quot; command line argument).</p>
<h3>Specifying the Java virtual machine</h3>
<p>Here is a typical Eclipse command line:&nbsp;</p>

<blockquote>
  <p><code>eclipse -vm c:\jdk6u22\jre\bin\javaw</code></p>
</blockquote>
<p><i>Tip:</i> It's generally a good idea to explicitly specify which Java VM to
use when running Eclipse. This is achieved with the &quot;<code>-vm</code>&quot;
command line argument as illustrated above. If you don't use &quot;<code>-vm</code>&quot;,
Eclipse will look on the OS path. When you install other Java-based products,
they may change your path and could result in a different Java VM being used
when you next launch Eclipse.</p>
<p>To create a Windows shortcut to an installed Eclipse:</p>
<ol>
  <li>Navigate to <code>eclipse.exe</code> in Windows Explorer and use Create
    Shortcut on the content menu.</li>
  <li>Select the shortcut and edit its Properties. In the Target: field append
    the command line arguments.</li>
</ol>
<p>Opening this shortcut launches Eclipse. (You can drag the shortcut to the 
Windows Desktop if you want to keep it in easy reach.)</p>

<h3><a name="macosx">Mac OS X</a></h3>
<p>On Mac OS X, you start Eclipse by double clicking the Eclipse application. If you need to 
pass arguments to Eclipse, you'll have to edit the <code>eclipse.ini</code> file
inside the Eclipse application bundle: select the Eclipse application bundle icon while holding down the Control Key.
This will present you with a popup menu. Select &quot;Show Package Contents&quot; in the popup menu.
Locate <code>eclipse.ini</code> file in the <code>Contents/MacOS</code> sub-folder and open it with your favorite text editor to edit the command line options.
</p>

<p>
On MacOS X you can only launch a UI program more than once if you have separate
copies of the program on disk. The reason for this behavior is that every UI
application on Mac can open multiple documents, so typically there is no need
to open a program twice. Since Eclipse cannot open more than one workspace, this means you have to make
a copy of the Eclipse install if you want to open more then one workspace at
the same time (bug <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=139319">139319</a>).
</p>

<p>If you need to launch Eclipse from the command line, you can use the symbolic link &quot;eclipse&quot; in the
top-level eclipse folder. It refers to the eclipse executable inside the application bundle and takes
the same arguments as &quot;eclipse.exe&quot; on other platforms. 
</p>
<p>On Mac OS X 10.4 and later, you may notice a slow down when working with significant
numbers of resources if you allow Spotlight to index your workspace. To prevent this, start
System Preferences, select the Spotlight icon, then the Privacy tab, then click the Add button
(&quot;+&quot;) and find your workspace directory in the dialog that appears.</p>
<h3><a name="SharedInstall">Shared Install</a></h3>
<p>The startup speed of a shared install can be improved if proper cache information is stored in the shared 
install area. To achieve this, after unzipping Eclipse distribution, run Eclipse once with the &quot;-initialize&quot; 
option from an account that has a write access to the install directory. 
See <a href=" http://help.eclipse.org/luna/index.jsp?topic=%2Forg.eclipse.platform.doc.isv%2Freference%2Fmisc%2Fmulti_user_installs.html">shared installs</a> in Eclipse 
Help for more information.</p>
<h2>5. <a name="Upgrading"></a>Upgrading Workspace from a Previous Release</h2>

<h3>Users who don't use &quot;-data&quot;</h3>
<p>If you weren't previously using &quot;-data&quot; to specify your workspace,
follow these steps to upgrade:</p>

<ol>
  <li>Find the workspace directory used by your old version of Eclipse.
    Typically this is located inside the directory in which Eclipse was
    installed in a sub-directory called &quot;<code>workspace</code>&quot;. If
    you are using a shortcut or script to launch Eclipse, then it will be under
    the current working directory of that shortcut or script in a sub-directory
    called &quot;workspace&quot;. For Windows users, this is specified by the
    &quot;Start in:&quot; argument in your shortcut properties.</li>
  <li>Copy this workspace directory to a new, empty location outside of any
    Eclipse install directory.</li>
  <li>Install the new version of Eclipse in a new location, separate from any
    old version of Eclipse.</li>
  <li>If you had installed additional features and plug-ins into your old
    Eclipse, you should re-install them in the new Eclipse.</li>
  <li>Start this new version of Eclipse and select
    this location using the workspace chooser dialog at startup (or use &quot;<code>-data</code>&quot;
    command line argument to pre-select the workspace location).</li>
</ol>
<h3>Users who do use &quot;-data&quot;</h3>
<p>If you were previously using the &quot;<code>-data</code>&quot; argument to
start Eclipse, your upgrade path is much easier:</p>

<ol>
  <li>Optionally copy your workspace directory to a new, empty location outside of any
    Eclipse install directory as a backup.</li>
  <li>Install the new version of Eclipse in a new location, separate from any
    old versions of Eclipse.</li>
  <li>If you had installed additional features and plug-ins into your old
    Eclipse, you should re-install them in the new Eclipse.</li>
  <li>Start this new version of Eclipse and select this location using the workspace chooser dialog at
    startup (or use &quot;<code>-data</code>&quot;
    command line argument to pre-select the workspace location).</li>
</ol>
<p><i>Note:</i> Copying your workspace is recommended because,
after you've upgraded your workspace, you won't be able to use it
again with an older version of Eclipse. If you ever want to go &quot;back in
time&quot; to an earlier release, you will need that backup.</p>
<h3> Users who use User Libraries or classpath containers that contain JARs referencing other libraries via Class-Path in the MANIFEST.MF</h3>
<p>
If you want the referenced JAR files to be included in the classpath, you can do one of the following:</p>
<ul>
<li>Add the system property (<code>-DresolveReferencedLibrariesForContainers=true</code>) to the <code>-vmargs</code>
list on start-up, or</li>
<li>Manually add the referenced JARs to the User Library or to the project.</li>
</ul>

<h3>Dropped in bundles may not resolve after upgrade</h3>
<p>If you have installed bundles by dropping them into the <tt>plugins</tt>
or <tt>dropins</tt> directory, they might no longer resolve when you upgrade
to a new Eclipse Platform version. In each new version of the Eclipse Platform,
there are new versions of bundles included in the platform, and often a small 
number of removed bundles. This may cause your previously dropped in bundles
to no longer resolve and run. It is always recommended that you install software
via the <tt>Help &gt; Install New Software</tt> mechanism so you are made
aware of any install-time failure to resolve dependencies.
</p>

<h2>6. <a name="InteroperabilityWithPreviousReleases">Interoperability with
Previous Releases</a></h2>
<h3>6.1 Interoperability of Release 4.4 with previous releases</h3>
<h4>Sharing projects between heterogeneous Eclipse 4.4 and 4.3</h4>
<p>Special care is required when a project in a team repository is being loaded
and operated on by developers using Eclipse-based products based on different
feature or plug-in versions. The general problem is that the existence,
contents, and interpretation of metadata files in the workspaces may be specific
to a particular feature or plug-in version, and differ between versions. The
workspace compatibility guarantees only cover cases where all developers upgrade
their Eclipse workspaces in lock step. In those cases there should be no problem
with shared metadata. However, when some developers are working in Eclipse 4.4
while others are working in Eclipse 3.x, there are no such guarantees.
This section provides advice for what to do and not to do. It addresses the
specific issues with the Eclipse SDK.</p>

<p>The typical failure mode is noticed by the 4.4 user. 4.4 metadata is lost
when a 4.3 user saves changes and then commits the updated metadata files to the
repository. Here's how things typically go awry:</p>
<ul>
  <li>A user working in Eclipse 4.4 creates or modifies a project in a way that
    results in changes to a shared metadata file that rely on 4.4-specific
    information. The user then commits the updated project files, including the
    shared metadata file, to the shared repository.</li>
  <li>Another user working in Eclipse 4.3 or earlier shares this project from the same
    repository. The 4.4-specific information in the shared metadata file is not
    understood by Eclipse 4.3, and is generally discarded or ignored without
    warning. The user modifies the project in a way that results in changes to
    the shared metadata file, causing the shared metadata file to be rewritten
    without any of the 4.4-specific information. The user commits the updated
    project files, including the shared metadata file, to the shared repository.
    The user is generally unaware that shared information has just been lost as
    a result of their actions.</li>
  <li>A user working in Eclipse 4.4 picks up the changes to a project from the
    shared repository, including the updated shared metadata file. The user may
    be unaware that they have just taken a retrograde step until later when
    things start to malfunction.</li>
</ul>
<p>Here are some things to watch out for when sharing projects between
Eclipse 4.4 and earlier releases:</p>
<ul>
  <li><b>Virtual folders</b> - 
  Eclipse 4.4 supports a notion of <i>virtual folders</i> that did not exist
  in Eclipse 3.5 or earlier. If such virtual folders are created in 4.4, and the project
  is subsequently loaded into an Eclipse 3.5 or earlier workspace, these folders
  will not be recognized. Recommendation: avoid creating virtual folders where project
  compatibility with Eclipse 3.5 or earlier is required.</li>
  <li><b>Resource filters</b> - 
  Eclipse 4.4 supports a notion of <i>resource filters</i> that did not exist
  in Eclipse 3.5 or earlier. If such filters are added to resources in 4.4, and the project
  is subsequently loaded into an Eclipse 3.5 or earlier workspace, these filters
  will not be recognized. Recommendation: avoid creating resource filters where project
  compatibility with Eclipse 3.5 or earlier is required.</li>
  <li><b>Predefined path variables</b> - 
  Eclipse 4.4 supports a set of built in path variables that can be used as the basis
  for linked resource locations. Such variables will not be defined automatically in 
  Eclipse 3.5 or earlier. If compatibility with 3.5 or earlier workspace is required,
  users on 3.5 or earlier workspaces will need to define such path variables manually.
  </li>
</ul>
<h4>Using Eclipse 4.4 to develop plug-ins that work in Eclipse 4.3</h4>
<p>It is also possible (and reasonable) to use Eclipse 4.4 to develop a plug-in 
  intended to work in Eclipse 4.3 or earlier. Use the <b>Plug-in Development &gt; 
  Target Platform </b>preference page to locate non-workspace plug-ins in an Eclipse 
  4.3 install. This ensures that the code for your plug-in is being compiled and 
  tested against Eclipse 4.3 APIs, extension points, and plug-ins. (The above 
  list of concerns do not apply since they affect the layout and interpretation 
  of files in the plug-in <i>project</i> but none affect the actual deployed form 
  of the plug-in.)</p>

<hr/>
<p>Sun, Solaris, Java and all Java-based trademarks are trademarks of Oracle Corporation.
in the United States, other countries, or both.</p>
<p>IBM is a trademark of International Business Machines Corporation in the
United States, other countries, or both.</p>
<p>Microsoft, Windows, Windows NT, Vista, and the Windows logo are trademarks of
Microsoft Corporation in the United States, other countries, or both.</p>
<p>Apple and Mac OS are trademarks of Apple Computer, Inc., registered in the
U.S. and other countries.</p>
<p>Other company, product, and service names may be trademarks or service marks
of others.</p>
<p>(c) Copyright Eclipse Contributors 2009, 2014</p>

<h2><a name="appendix">Appendix 1: Execution Environment by Bundle</a></h2>

<p>In the table below, the &quot;4.4 minimum execution environment&quot;
  column indicates the minimum Java class library requirements of each bundle
  for the 4.4 release, where the value is one of:</p>
<table border="0" width="90%">
  <tbody>
    <tr>

      <td align="center"><b>Entry</b></td>
      <td align="left"><b>Meaning</b></td>
    </tr>
    <tr>
      <td><div align="center"><strong>F1.0</strong></div></td>
      <td>J2ME Foundation 1.0 - indicates that the bundle can only be run on
        Foundation 1.0 or greater. Note that with the exception of some MicroEdition
        IO classes, Foundation 1.0 is a subset of J2SE 1.3.</td>
    </tr>
    <tr>
      <td><div align="center"><strong>F1.1</strong></div></td>
      <td>J2ME Foundation 1.1 - indicates that the bundle can only be run on
        Foundation 1.1 or greater. Note that with the exception of some MicroEdition
        IO classes, Foundation 1.1 is a subset of J2SE 1.4.</td>
    </tr>
    <tr>
      <td><div align="center"><strong>1.2</strong></div></td>
      <td>J2SE 1.2 - indicates that the bundle can only be run on JSE 1.2 or
        greater.</td>
    </tr>
    <tr>
      <td><div align="center"><strong>1.3</strong></div></td>
      <td>J2SE 1.3 - indicates that the bundle can only be run on JSE 1.3 or
        greater.</td>
    </tr>
    <tr>
      <td><div align="center"><strong>1.4</strong></div></td>
      <td>J2SE 1.4 - indicates that the bundle can only be run on JSE 1.4 or
        greater.</td>
    </tr>
    <tr>
      <td><div align="center"><strong>1.5</strong></div></td>
      <td>Java SE 5 - indicates that the bundle can only be run on Java SE 5 or
        greater.</td>
    </tr>
    <tr>
      <td><div align="center"><strong>1.6</strong></div></td>
      <td>Java SE 6 - indicates that the bundle can only be run on Java SE 6 or
        greater.</td>
    </tr>
    <tr>
      <td><div align="center"><strong>1.7</strong></div></td>
      <td>Java SE 7 - indicates that the bundle can only be run on Java SE 7 or
        greater.</td>
    </tr>
    <tr>
      <td><div align="center"><strong>1.8</strong></div></td>
      <td>Java SE 8 - indicates that the bundle can only be run on Java SE 8 or
        greater.</td>
    </tr>
    <tr>
      <td align="center"><b>n/a</b></td>
      <td>Unknown at the time of this revision.</td>
    </tr>
  </tbody>
</table>

<p><b>Table of minimum execution environments by bundle.</b> (See also the
  <a href="https://projects.eclipse.org/projects/rt.equinox/releases/4.4.0/plan">Equinox Project plan</a>
  for the execution environment requirements of bundles contributed via that project.)</p>

<table border="1">
  <tbody>
    <tr>
      <td width="290"><strong>Bundle</strong></td>
      <td width="60"><div align="center"><p align="center"><b>4.4<br/>minimum<br/>execution<br/>environment</b></p></div></td>
    </tr>
    <tr>
      <td>com.ibm.icu</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>com.jcraft.jsch</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>com.sun.el</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>javax.annotation</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>javax.el</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>javax.inject</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>javax.servlet</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>javax.servlet.jsp</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>javax.xml</td>
      <td><div align="center">1.2</div></td>
    </tr>
    <tr>
      <td>org.apache.ant</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.apache.batik.css</td>
      <td><div align="center">1.3</div></td>
    </tr>
    <tr>
      <td>org.apache.batik.util</td>
      <td><div align="center">1.3</div></td>
    </tr>
    <tr>
      <td>org.apache.batik.util.gui</td>
      <td><div align="center">1.3</div></td>
    </tr>
    <tr>
      <td>org.apache.commons.codec</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.apache.commons.logging</td>
      <td><div align="center">F1.0</div></td>
    </tr>
    <tr>
      <td>org.apache.felix.gogo.command</td>
      <td><div align="center">not specified</div></td>
    </tr>
    <tr>
      <td>org.apache.felix.gogo.runtime</td>
      <td><div align="center">not specified</div></td>
    </tr>
    <tr>
      <td>org.apache.felix.gogo.shell</td>
      <td><div align="center">not specified</div></td>
    </tr>
    <tr>
      <td>org.apache.httpcomponents.httpclient</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.apache.httpcomponents.httpcore</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.apache.jasper.glassfish</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.apache.lucene.analysis</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.apache.lucene.core</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ant.core</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ant.launching</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ant.ui</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.compare</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.compare.core</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.compare.win32</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.core.commands</td>
      <td><div align="center">F1.0</div></td>
    </tr>
    <tr>
      <td>org.eclipse.core.contenttype</td>
      <td><div align="center">F1.0</div></td>
    </tr>
    <tr>
      <td>org.eclipse.core.databinding</td>
      <td><div align="center">F1.1</div></td>
    </tr>
    <tr>
      <td>org.eclipse.core.databinding.beans</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.core.databinding.observable</td>
      <td><div align="center">F1.1</div></td>
    </tr>
    <tr>
      <td>org.eclipse.core.databinding.property</td>
      <td><div align="center">F1.1</div></td>
    </tr>
    <tr>
      <td>org.eclipse.core.expressions</td>
      <td><div align="center">F1.0</div></td>
    </tr>
    <tr>
      <td>org.eclipse.core.externaltools</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.core.filebuffers</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.core.filesystem</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.core.filesystem.java7</td>
      <td><div align="center">1.7</div></td>
    </tr>
    <tr>
      <td>org.eclipse.core.jobs</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.core.net</td>
      <td><div align="center">F1.1</div></td>
    </tr>
    <tr>
      <td>org.eclipse.core.resources</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.core.runtime</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.core.runtime.compatibility</td>
      <td><div align="center">F1.0</div></td>
    </tr>
    <tr>
      <td>org.eclipse.core.runtime.compatibility.registry</td>
      <td><div align="center">F1.0</div></td>
    </tr>
    <tr>
      <td>org.eclipse.core.variables</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.cvs</td>
      <td><div align="center">not specified</div></td>
    </tr>
    <tr>
      <td>org.eclipse.debug.core</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.debug.ui</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.e4.core.commands</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.e4.core.contexts</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.e4.core.di</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.e4.core.di.extensions</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.e4.core.services</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.e4.ui.bindings</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.e4.ui.css.core</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.e4.ui.css.swt</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.e4.ui.css.swt.theme</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.e4.ui.di</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.e4.ui.model.workbench</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.e4.ui.services</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.e4.ui.widgets</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.e4.ui.workbench</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.e4.ui.workbench.addons.swt</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.e4.ui.workbench.renderers.swt</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.e4.ui.workbench.swt</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.e4.ui.workbench3</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.emf.common</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.emf.ecore</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.emf.ecore.change</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.emf.ecore.xmi</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.help</td>
      <td><div align="center">F1.0</div></td>
    </tr>
    <tr>
      <td>org.eclipse.help.base</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.help.ui</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.help.webapp</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt</td>
      <td><div align="center">not specified</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt.annotation</td>
      <td><div align="center">1.8</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt.apt.core</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt.apt.pluggable.core</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt.apt.ui</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt.compiler.apt</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt.compiler.tool</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt.core</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt.core.manipulation</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt.debug</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt.debug.ui</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt.doc.isv</td>
      <td><div align="center">not specified</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt.doc.user</td>
      <td><div align="center">not specified</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt.junit</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt.junit.core</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt.junit.runtime</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt.junit4.runtime</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt.launching</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jdt.ui</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jface</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jface.databinding</td>
      <td><div align="center">F1.0</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jface.text</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jsch.core</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.jsch.ui</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ltk.core.refactoring</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ltk.ui.refactoring</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.pde</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.pde.api.tools</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.pde.api.tools.annotations</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.pde.api.tools.ui</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.pde.build</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.pde.core</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.pde.doc.user</td>
      <td><div align="center">not specified</div></td>
    </tr>
    <tr>
      <td>org.eclipse.pde.ds.core</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.pde.ds.ui</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.pde.junit.runtime</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.pde.launching</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.pde.runtime</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.pde.ua.core</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.pde.ua.ui</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.pde.ui</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.pde.ui.templates</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.platform</td>
      <td><div align="center">F1.0</div></td>
    </tr>
    <tr>
      <td>org.eclipse.platform.doc.isv</td>
      <td><div align="center">not specified</div></td>
    </tr>
    <tr>
      <td>org.eclipse.platform.doc.user</td>
      <td><div align="center">not specified</div></td>
    </tr>
    <tr>
      <td>org.eclipse.rcp</td>
      <td><div align="center">not specified</div></td>
    </tr>
    <tr>
      <td>org.eclipse.sdk</td>
      <td><div align="center">not specified</div></td>
    </tr>
    <tr>
      <td>org.eclipse.search</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.swt</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.team.core</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.team.cvs.core</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.team.cvs.ssh2</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.team.cvs.ui</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.team.ui</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.text</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui</td>
      <td><div align="center">F1.0</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.browser</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.cheatsheets</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.console</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.editors</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.externaltools</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.forms</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.ide</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.ide.application</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.intro</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.intro.universal</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.navigator</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.navigator.resources</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.net</td>
      <td><div align="center">F1.1</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.themes</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.trace</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.views</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.views.log</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.views.properties.tabbed</td>
      <td><div align="center">F1.0</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.win32</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.workbench</td>
      <td><div align="center">1.6</div></td>
    </tr>
    <tr>
      <td>org.eclipse.ui.workbench.texteditor</td>
      <td><div align="center">1.4</div></td>
    </tr>
    <tr>
      <td>org.eclipse.update.configurator</td>
      <td><div align="center">F1.0</div></td>
    </tr>
    <tr>
      <td>org.hamcrest.core</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.junit</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.objectweb.asm</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.objectweb.asm.tree</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.sat4j.core</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.sat4j.pb</td>
      <td><div align="center">1.5</div></td>
    </tr>
    <tr>
      <td>org.w3c.css.sac</td>
      <td><div align="center">F1.0</div></td>
    </tr>
    <tr>
      <td>org.w3c.dom.events</td>
      <td><div align="center">1.3</div></td>
    </tr>
    <tr>
      <td>org.w3c.dom.smil</td>
      <td><div align="center">F1.0</div></td>
    </tr>
    <tr>
      <td>org.w3c.dom.svg</td>
      <td><div align="center">F1.0</div></td>
    </tr>
  </tbody>
</table>

</body>
</html>
