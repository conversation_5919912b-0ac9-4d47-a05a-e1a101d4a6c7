<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Constructor Functions</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch03.html" title="How to use XPath 2.0 functions with PsychoPath"><link rel="prev" href="ch03.html" title="How to use XPath 2.0 functions with PsychoPath"><link rel="next" href="ch03s03.html" title="Functions on Numeric Values"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Constructor Functions"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Constructor_Functions"></a>Constructor Functions</h2></div></div></div><div class="literallayout"><p>xs:dateTime("2002-02-01T10:00:00+06:00")<br>
</p></div><p>from within a Java application, in order to extract the result from the result sequence, one would have to use this code: </p><p>String n = ((XSDateTime)rs.first()).stringvalue(); println(n);</p><p>in order to get the result of &lsquo;2002-02-01T04:00:00Z&rsquo;</p></div></body></html>