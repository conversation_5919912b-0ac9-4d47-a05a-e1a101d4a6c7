/*******************************************************************************
 * Copyright (c) 2006, 2009 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials 
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * 
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *******************************************************************************/

/*
 * We are not using titles on this page.
 */
.intro-header {
	display : none;
}

/*
 * We will not use the general-purpose group1 used in
 * other pages for a curve image.
 */

#extra-group1 {
	display : none;
}


#page-links a .link-label, #action-links a .link-label {
	font-weight : 600;
	color : #E5E5E5;
}

#page-links a p .text, #action-links a p .text {
	font-weight : 500;
	color : #E5E5E5;
}

/*
 * Set up the content for the standby page.
 */
body {
	min-width : 230px;
	/* since IE doesn't support min-width, use expression */
	width:expression(document.body.clientWidth < 230? "230px": "auto" );
	background-repeat : no-repeat;
	background-position : top left;
	background-color : #6d7e85;
}

.page {
	background-repeat : no-repeat;
	background-position : bottom left;
	min-width : 230px;
	/* since IE doesn't support min-width, use expression */
	width:expression(document.body.clientWidth < 230? "230px": "auto" );
 	min-height : 610px;
	height : 100%;
	height : expression(document.body.clientHeight < 450? "450px": "100%" );
}

/* 
 * Set up the navigation bar.  It should be centered in the middle
 * of the page
 */

#links-background { 
	width : 100%; 
 	margin-top : 10%; 
	margin-bottom : auto;
	text-align : center;
}

#page-links a {
	display : block;
	width : 220px;
	text-align : left; 
	margin-left : auto;
	margin-right : auto;
	margin-top : 0px;
	vertical-align : top;
}

#page-links a span, #page-links a p {
	display : block;
	width : 160px;
	margin : 0px;
	padding : 0px;
}

#page-links a .link-label {
	position : relative;
	left : 60px;
	top : -50px;
}

#page-links a p .text {
	position : relative;
	left : 60px;
	top : -50px;
}

#page-links a img {
	height : 52px;
	width : 52px;
	vertical-align : middle;
}

#page-links a:hover,
#page-links a:focus,
#page-links a:active  { border : 0px; }

#page-links a:hover p,
#page-links a:focus p,
#page-links a:active p  { margin : 0px; padding : 0px; }

/* properties for each of the page-links  */

#page-links a .background-image {
	display: none;
}

#page-links a .link-extra-div {
	display :none;
}

a#overview img { background-image : url(../graphics/standby/ov_standby.gif); }
a#overview:hover img,
a#overview:focus img,
a#overview:active img { background-image : url(../graphics/standby/ov_standbyhov.gif); }

a#firststeps img { background-image : url(../graphics/standby/fs_standby.gif); }
a#firststeps:hover img,
a#firststeps:focus img,
a#firststeps:active img { background-image : url(../graphics/standby/fs_standbyhov.gif); }

a#tutorials img { background-image : url(../graphics/standby/tu_standby.gif); }
a#tutorials:hover img,
a#tutorials:focus img,
a#tutorials:active img { background-image : url(../graphics/standby/tu_standbyhov.gif); }

a#samples img { background-image : url(../graphics/standby/sa_standby.gif); }
a#samples:hover img,
a#samples:focus img,
a#samples:active img { background-image : url(../graphics/standby/sa_standbyhov.gif); }

a#whatsnew img { background-image : url(../graphics/standby/wn_standby.gif); }
a#whatsnew:hover img,
a#whatsnew:focus img,
a#whatsnew:active img { background-image : url(../graphics/standby/wn_standbyhov.gif); }

a#webresources img { background-image : url(../graphics/standby/wr_standby.gif); }
a#webresources:hover img,
a#webresources:focus img,
a#webresources:active img { background-image : url(../graphics/standby/wr_standbyhov.gif); }

a#migrate img { background-image : url(../graphics/standby/mi_standby.gif); }
a#migrate:hover img,
a#migrate:focus img,
a#migrate:active img { background-image : url(../graphics/standby/mi_standbyhov.gif); }

a#workbench img { background-image : url(../graphics/standby/wb_standby.gif); }
a#workbench:hover img,
a#workbench:focus img,
a#workbench:active img { background-image : url(../graphics/standby/wb_standbyhov.gif); }
