/*******************************************************************************
 * Copyright (c) 2008 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials 
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * 
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *******************************************************************************/

.intro-header H1 {
	font-size : 16pt
}

h2 {
	font-size : 120%;
}

/* For regular div labels */
H4 .div-label {
	font-size : 100%;
}

/* The label part of the folding section */
.section-title-link .section-title {
	font-size : 100%;
}

/* For the main page content's title */
#content-header H4 .div-label {
	font-size : 160%;
}

.page-description { 
	font-size : 100%;
}

a .link-label {
	font-size : 100%;
}

#navigation-links a .link-label {
	font-size : 90%;
}

a .text {
	font-size : 90%;
}

p .group-description {
	font-size : 100%;
}

.categoryContentnav {
	//font-size: 9pt; 
}

.topicList {
	font-size: 90%; 
}

/* 
 * Set up general font colours, sizes, etc.  Some of these will override
 * settings from the shared CSS 
 */
#root .intro-header H1 {
	font-size : 200%;
}
#root #page-links a .link-label, #root #action-links a .link-label {
	font-size : 13pt;
}

#root #page-links a p .text, #root #action-links a p .text {
	font-size : 13pt;
}

#standby .intro-header H1 {
	font-size : 150%;
}

#standby #page-links a .link-label, #standby #action-links a .link-label {
	font-size : 100%;
}

#standby #page-links a p .text, #standby #action-links a p .text {
	font-size : 100%;
}
