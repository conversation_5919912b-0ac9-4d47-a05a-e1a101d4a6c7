<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.0"?>
<plugin>

     <extension id="ide" point="org.eclipse.core.runtime.products"> 
      <product name="%productName" application="org.eclipse.ui.ide.workbench" description="%productBlurb"> 
          <!-- For documentation on updating icons, see http://wiki.eclipse.org/Platform-releng/Updating_Branding -->
          <property name="windowImages" value="eclipse16.png,eclipse32.png,eclipse48.png"/> 
          <property name="aboutImage" value="eclipse_lg.png"/> 
          <property name="aboutText" value="%productBlurb"/> 
          <property name="appName" value="Eclipse"/> 
          <property name="preferenceCustomization" value="plugin_customization.ini"/> 
         <property
          		name="introTitle"
          		value="%productIntroTitle"/>
          <property
          		name="introBrandingImage"
          		value="product:intro-eclipse.png"/>
          <property
          		name="introBrandingImageText"
          		value="%productIntroBrandingText"/>
		  <property
				name="introDescription-overview"
				value="%introDescription-overview"/>
		  <property
				name="introDescription-tutorials"
				value="%introDescription-tutorials"/>
		  <property
				name="introDescription-samples"
				value="%introDescription-samples"/>
		  <property
				name="applicationXMI"
				value="org.eclipse.platform/LegacyIDE.e4xmi">
		  </property>
		  <property
				name="cssTheme"
				value="org.eclipse.e4.ui.css.theme.e4_default">
		  </property>
		  <property
				name="applicationCSSResources"
          value="platform:/plugin/org.eclipse.ui.themes/images/">
		  </property>

          <property
        		name="startupForegroundColor"
        		value="9c9696"/>
          <property
                name="startupMessageRect"
                value="7,265,320,20"/>
          <property
                name="startupProgressRect"
                value="2,290,448,10"/>
          <property
                name="buildIdLocation"
                value="0,220">
          </property>
          <property
                name="buildIdSize"
                value="419,40">
          </property>

      </product> 
   </extension> 

   	<extension
		point="org.eclipse.ui.intro">
      <introProductBinding
            introId="org.eclipse.ui.intro.universal"
            productId="org.eclipse.platform.ide">
      </introProductBinding>
    </extension>

   <extension
         point="org.eclipse.ui.cheatsheets.cheatSheetContent">
      <category
            id="org.eclipse.platform.team"
            name="%cheatsheet.category.team">
      </category>
      <cheatsheet
            category="org.eclipse.platform.team"
            contentFile="$nl$/cheatsheets/cvs_checkout.xml"
            id="org.eclipse.platform.cvs.checkout"
            name="%cheatsheet.cvs.checkout.name">
         <description>
            %cheatsheet.cvs.checkout.desc
         </description>
      </cheatsheet>
      <cheatsheet
            category="org.eclipse.platform.team"
            contentFile="$nl$/cheatsheets/cvs_merge.xml"
            id="org.eclipse.platform.cvs.merge"
            name="%cheatsheet.cvs.merge.name">
         <description>
            %cheatsheet.cvs.merge.desc
         </description>
      </cheatsheet>
   </extension>
   <!-- =====================================================  -->
   <!-- Standby Content Part contributions                     -->
   <!-- =====================================================  -->
   <extension point="org.eclipse.ui.intro.configExtension">
      <standbyContentPart
            id="org.eclipse.platform.cheatsheet"
            class="org.eclipse.platform.internal.CheatSheetStandbyContent"
            pluginId="org.eclipse.platform"/>
      <configExtension
            configId="org.eclipse.ui.intro.universalConfig"
            content="$nl$/intro/overviewExtensionContent.xml"/>
      <configExtension
            configId="org.eclipse.ui.intro.universalConfig"
            content="$nl$/intro/tutorialsExtensionContent.xml"/>
      <configExtension
            configId="org.eclipse.ui.intro.universalConfig"
            content="$nl$/intro/whatsnewExtensionContent1.xml"/>
      <configExtension
            configId="org.eclipse.ui.intro.universalConfig"
            content="$nl$/intro/whatsnewExtensionContent2.xml"/>
      <configExtension
            configId="org.eclipse.ui.intro.universalConfig"
            content="$nl$/intro/whatsnewExtensionContent3.xml"/>
      <configExtension
            configId="org.eclipse.ui.intro.universalConfig"
            content="$nl$/intro/migrateExtensionContent.xml"/>
   </extension>


   <extension
         point="org.eclipse.ui.actionSets">
      <actionSet
            label="%cheatsheet.actionset"
            visible="true"
            id="org.eclipse.ui.cheatsheets.actionSet">
         <action
               label="%cheatsheet.item"
               class="org.eclipse.ui.cheatsheets.CheatSheetExtensionFactory:helpMenuAction"
               menubarPath="help/group.tutorials"
               id="org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction">
         </action>
      </actionSet>
   </extension>
  

</plugin>
