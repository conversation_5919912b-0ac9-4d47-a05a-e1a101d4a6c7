/*******************************************************************************
 * Copyright (c) 2014 <PERSON> and others.
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *     <PERSON> <<EMAIL>> - initial API and implementation
 *     Andrea <PERSON>ni - intial color schema definition
 *******************************************************************************/

/* ############################## Eclipse UI properties ############################## */


IEclipsePreferences#org-eclipse-ui-editors {
	preferences:
		'AbstractTextEditor.Color.SelectionForeground.SystemDefault=false'
		'AbstractTextEditor.Color.SelectionBackground.SystemDefault=false'
		'AbstractTextEditor.Color.Background.SystemDefault=false'
		'AbstractTextEditor.Color.Foreground.SystemDefault=false'
		'AbstractTextEditor.Color.Background=32,32,32'
		'AbstractTextEditor.Color.FindScope=30,120,155'
		'AbstractTextEditor.Color.Foreground=217,232,247'
		'AbstractTextEditor.Color.SelectionBackground=62,81,93'
		'AbstractTextEditor.Color.SelectionForeground=68,206,239'
		'asOccurencesIndicationColor=72,72,72'
		'breakpointIndicationColor=51,119,193'
		'currentIPColor=54,54,54'
		'currentLineColor=54,54,54'
		'deletionIndicationColor=134,60,67'
		'errorIndicationColor=179,26,64'
		'errorIndicationHighlighting=true'
		'filteredSearchResultIndicationColor=110,110,110'
		'hyperlinkColor=102,175,249'
		'hyperlinkColor.SystemDefault=false'
		'infoIndicationColor=86,194,170'
		'lineNumberColor=98,98,98'
		'linked.slave.color=66,156,255'
		'matchingTagIndicationColor=72,72,72'
		'occurrenceIndicationColor=72,72,72'
		'overrideIndicatorColor=78,120,117'
		'printMarginColor=98,98,98'
		'searchResultHighlighting=false'
		'searchResultIndication=true'
		'searchResultIndicationColor=94,94,94'
		'searchResultTextStyle=BOX'
		'secondaryIPColor=54,54,54'
		'spellingIndicationColor=253,170,211'
		'warningIndicationColor=156,114,3'
		'warningIndicationHighlighting=true'
		'writeOccurrenceIndicationColor=51,90,114'
}

IEclipsePreferences#org-eclipse-ui-workbench {
	preferences:
		'ACTIVE_HYPERLINK_COLOR=138,201,242'
		'CONFLICTING_COLOR=240,15,66'
		'CONTENT_ASSIST_BACKGROUND_COLOR=52,57,61'
		'CONTENT_ASSIST_FOREGROUND_COLOR=238,238,238'
		'ERROR_COLOR=247,68,117'
		'HYPERLINK_COLOR=111,197,238'
		'INCOMING_COLOR=31,179,235'
		'OUTGOING_COLOR=238,238,238'
		'RESOLVED_COLOR=108,210,17'
}

