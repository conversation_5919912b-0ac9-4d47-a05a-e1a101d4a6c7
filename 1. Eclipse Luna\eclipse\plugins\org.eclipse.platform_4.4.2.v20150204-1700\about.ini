# about.ini
# contains information about a feature
# java.io.Properties file (ISO 8859-1 with "\" escapes)
# "%key" are externalized strings defined in about.properties
# This file does not need to be translated.

# Property "aboutText" contains blurb for feature details in the "About" 
# dialog (translated).  Maximum 15 lines and 75 characters per line.
aboutText=%blurb

# Property "featureImage" contains path to feature image (32x32)
featureImage=eclipse32.png

# Property "welcomePage" contains path to welcome page (special XML-based format)
# ($nl$/ prefix to permit locale-specific translations of entire file)
welcomePage=$nl$/welcome.xml

# Property "welcomePerspective" contains the id of the perspective in which the
# welcome page is to be opened.
# optional

# Property "tipsAndTricksHref" contains the Help topic href to a tips and tricks page 
# optional
tipsAndTricksHref=/org.eclipse.platform.doc.user/tips/platform_tips.html

