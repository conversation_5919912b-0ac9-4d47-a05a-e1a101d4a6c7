<toc topic="html/index.html" label="XPath 2.0 Processor User Manual"><topic href="html/ch01.html" label="Introduction"><topic href="html/ch01.html#Getting_PsychoPath" label="Getting PsychoPath"></topic></topic><topic href="html/ch02.html" label="Using PsychoPath XPath 2.0 API"><topic href="html/ch02.html#Non-Schema_Aware" label="Non-Schema Aware"></topic><topic href="html/ch02s02.html" label="Schema Aware"></topic><topic href="html/ch02s03.html" label="How to use the XPath 2.0 grammar with PsychoPath"><topic href="html/ch02s03.html#Constants" label="Constants"></topic><topic href="html/ch02s03s02.html" label="Path expressions"></topic><topic href="html/ch02s03s03.html" label="Axis steps"></topic><topic href="html/ch02s03s04.html" label="Set difference, intersection and Union"></topic><topic href="html/ch02s03s05.html" label="Arithmetic Expressions"><topic href="html/ch02s03s05.html#Unary" label="Unary"></topic><topic href="html/ch02s03s05s02.html" label="Multiplication and Division:"></topic><topic href="html/ch02s03s05s03.html" label="Addition and Subtraction:"></topic></topic><topic href="html/ch02s03s06.html" label="Range expressions"></topic><topic href="html/ch02s03s07.html" label="Comparisons"></topic><topic href="html/ch02s03s08.html" label="Conditional Expressions"></topic><topic href="html/ch02s03s09.html" label="Quantified Expressions"></topic><topic href="html/ch02s03s10.html" label="And, Or Expressions"></topic><topic href="html/ch02s03s11.html" label="SequenceType Matching Expressions"></topic></topic></topic><topic href="html/ch03.html" label="How to use XPath 2.0 functions with PsychoPath"><topic href="html/ch03.html#Accessors" label="Accessors"></topic><topic href="html/ch03s02.html" label="Constructor Functions"></topic><topic href="html/ch03s03.html" label="Functions on Numeric Values"></topic><topic href="html/ch03s04.html" label="Functions to Assemble and Disassemble Strings"></topic><topic href="html/ch03s05.html" label="Compare and Other Functions on String Values"></topic><topic href="html/ch03s06.html" label="Functions Based on Substring Matching"></topic><topic href="html/ch03s07.html" label="String Functions that Use Pattern Matching"></topic><topic href="html/ch03s08.html" label="Functions on Boolean Values"></topic><topic href="html/ch03s09.html" label="Component Extraction Functions on Durations, Dates and Times"></topic><topic href="html/ch03s10.html" label="Functions Related to QNames"></topic><topic href="html/ch03s11.html" label="Functions on Nodes"><topic href="html/ch03s11.html#General_Functions_on_Sequences" label="General Functions on Sequences"></topic><topic href="html/ch03s11s02.html" label="Functions That Test the Cardinality of Sequences"></topic><topic href="html/ch03s11s03.html" label="Deep-Equal, Aggregate Functions, and Functions that Generate Sequences"></topic><topic href="html/ch03s11s04.html" label="Context Functions"></topic></topic></topic><topic href="html/ch04.html" label="How to use XPath 2.0 operators with PsychoPath"><topic href="html/ch04.html#Operators_on_Numeric_Values" label="Operators on Numeric Values"></topic><topic href="html/ch04s02.html" label="Comparison of Numeric Values"></topic><topic href="html/ch04s03.html" label="Operators on Boolean Values"></topic><topic href="html/ch04s04.html" label="Comparisons of Duration, Date and Time Values"></topic><topic href="html/ch04s05.html" label="Arithmetic Functions on Durations"></topic><topic href="html/ch04s06.html" label="Arithmetic Functions Dates and Times"></topic><topic href="html/ch04s07.html" label="Operators Related to QNames And Nodes"></topic><topic href="html/ch04s08.html" label="Union, Intersection and Except"></topic><topic href="html/ch04s09.html" label="Operators that Generate Sequences"></topic></topic></toc>