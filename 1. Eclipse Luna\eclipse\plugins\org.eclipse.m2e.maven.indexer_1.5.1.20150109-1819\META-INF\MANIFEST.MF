Manifest-Version: 1.0
Bundle-DocURL: www.eclipse.org
Bundle-RequiredExecutionEnvironment: JavaSE-1.6,JavaSE-1.7
Built-By: ifedorenk
Bundle-SymbolicName: org.eclipse.m2e.maven.indexer;singleton:=false
Require-Bundle: org.eclipse.m2e.maven.runtime;bundle-version="[1.5.0,1
 .6.0)",org.eclipse.m2e.archetype.common;bundle-version="[1.5.0,1.6.0)
 "
Export-Package: org.apache.lucene,org.apache.lucene.analysis,org.apach
 e.lucene.analysis.standard,org.apache.lucene.document,org.apache.luce
 ne.index,org.apache.lucene.index.memory,org.apache.lucene.queryParser
 ,org.apache.lucene.search,org.apache.lucene.search.function,org.apach
 e.lucene.search.highlight,org.apache.lucene.search.payloads,org.apach
 e.lucene.search.spans,org.apache.lucene.store,org.apache.lucene.util,
 org.apache.lucene.util.cache,org.apache.maven.index,org.apache.maven.
 index.archetype,org.apache.maven.index.artifact,org.apache.maven.inde
 x.cli,org.apache.maven.index.context,org.apache.maven.index.creator,o
 rg.apache.maven.index.fs,org.apache.maven.index.incremental,org.apach
 e.maven.index.locator,org.apache.maven.index.packer,org.apache.maven.
 index.search.grouping,org.apache.maven.index.treeview,org.apache.mave
 n.index.updater,META-INF.plexus
Bundle-Version: 1.5.1.20150109-1819
Build-Jdk: 1.7.0_51
Bundle-ClassPath: .,indexer-core-3.1.0.jar,indexer-artifact-3.1.0.jar,
 lucene-core-2.4.1.jar,lucene-highlighter-2.4.1.jar
Bundle-Vendor: %Bundle-Vendor
Bnd-LastModified: 1420827577061
Bundle-Name: %Bundle-Name
Tool: Bnd-2.1.0.20130426-122213
Embed-Transitive: true
Eclipse-BundleShape: dir
Created-By: Apache Maven Bundle Plugin
Embedded-Artifacts: indexer-core-3.1.0.jar;g="org.apache.maven.indexer
 ";a="indexer-core";v="3.1.0",indexer-artifact-3.1.0.jar;g="org.apache
 .maven.indexer";a="indexer-artifact";v="3.1.0",lucene-core-2.4.1.jar;
 g="org.apache.lucene";a="lucene-core";v="2.4.1",lucene-highlighter-2.
 4.1.jar;g="org.apache.lucene";a="lucene-highlighter";v="2.4.1"
Bundle-ManifestVersion: 2
Embed-Dependency: *;scope=compile|runtime

Name: about_files/LICENSE-2.0.txt
SHA1-Digest: K4uBUimqimHkg/tLoFiLi2xJGJA=

Name: org/apache/maven/index/NexusIndexer.class
SHA1-Digest: KmD1h/dFX9I98X/dutp3Ipweu7o=

Name: org/apache/maven/index/ArtifactInfo$RepositoryVersionComparator.
 class
SHA1-Digest: CEBwVo7nfM0hl6e1k9K2fEGMyvo=

Name: indexer-core-3.1.0.jar
SHA1-Digest: erZ+ayDlMyp/tP3y8BmuxCdYRsI=

Name: org/apache/maven/index/SearchEngine.class
SHA1-Digest: pPqJV02nupOf1QQaNwh2LymYXsU=

Name: org/apache/maven/index/ArtifactAvailablility.class
SHA1-Digest: 4Z9E4ETvieFQRyyGSTdKZ1wVrIs=

Name: org/apache/maven/index/IndexerFieldVersion.class
SHA1-Digest: weC9AMLb+jiLPIRCtICQUOzZCMc=

Name: lucene-highlighter-2.4.1.jar
SHA1-Digest: C6qc4zrEsHUBFS2PLcLf5EMJBWM=

Name: org/apache/maven/index/ArtifactInfoRecord.class
SHA1-Digest: ePlY9i8OI7e/DgcMzqEFNJiuP/Y=

Name: org/apache/maven/index/MatchHighlightRequest.class
SHA1-Digest: hZGoLpI1cxTxUOWgsMOcvdzdkC4=

Name: org/apache/maven/index/DefaultQueryCreator.class
SHA1-Digest: WxG0rP1PTf6pROKAUk0qm2y6A4o=

Name: org/apache/maven/index/IteratorSearchResponse.class
SHA1-Digest: d+Ksxd1we3CX0T8DPe9/hsxzdeY=

Name: org/apache/maven/index/ArtifactInfo.class
SHA1-Digest: 1eIx/ISMwZ8CkconiUCW6ERrV/g=

Name: org/apache/maven/index/ArtifactContextProducer.class
SHA1-Digest: Tg9M4JG4yX8A7ycEm93z9hgcQ5g=

Name: org/apache/maven/index/FlatSearchResponse.class
SHA1-Digest: s+S8WwZNaXxkxNxotNOP1qJhUrc=

Name: org/apache/maven/index/ArtifactInfoPostprocessor.class
SHA1-Digest: PyeIJVUYHmqbCmSEvIyEB/rcljo=

Name: org/apache/maven/index/IteratorSearchRequest.class
SHA1-Digest: eENqTO365U8R/Aq3axVasK4nTf0=

Name: org/apache/maven/index/UniqueGAArtifactFilterPostprocessor.class
SHA1-Digest: lskoHHnEGOpzCjoxSYIkO/g9NWI=

Name: org/apache/maven/index/Scanner.class
SHA1-Digest: zjw7FLE27uAw/PyjJsLIwUtJV4Y=

Name: org/apache/maven/index/NEXUS.class
SHA1-Digest: Z+eNU/m2iU3IooROCrICzuiyVww=

Name: org/apache/maven/index/ArtifactScanningListener.class
SHA1-Digest: mwOdC37BF3GxStz7AZutSHc7qKw=

Name: org/apache/maven/index/GroupedSearchResponse.class
SHA1-Digest: aSyaq7IVGQazK3mz+KiUqef1rKw=

Name: org/apache/maven/index/AndMultiArtifactInfoFilter.class
SHA1-Digest: 9rqYoDTrO9qi9sj+QaWowd9eFNw=

Name: org/apache/maven/index/MAVEN.class
SHA1-Digest: 4ZTqgZhFAyZvf5W1redi5SGRMfs=

Name: org/apache/maven/index/FlatSearchRequest.class
SHA1-Digest: dd57wy5D+labSkTUEr8fJJTBCgE=

Name: org/apache/maven/index/AbstractMultiArtifactInfoFilter.class
SHA1-Digest: P0coes1lfLgcg85Fz1hpZCzUR54=

Name: org/apache/maven/index/SearchType.class
SHA1-Digest: I1iDr1oH4Clm+R4OpAhLDrAbYvI=

Name: org/apache/maven/index/Grouping.class
SHA1-Digest: OcPML0NHzVbNCNjX6wHGRf3qOtw=

Name: org/apache/maven/index/QueryCreator.class
SHA1-Digest: RwmQJVy0Nw8esabLF+VZ9GuK8tw=

Name: org/apache/maven/index/ArtifactDiscoveryListener.class
SHA1-Digest: uYeHU/9BUjt+U4QShsTI0F2EQM0=

Name: lucene-core-2.4.1.jar
SHA1-Digest: 2X8PjoVAQKYqDJsbbCzBDeMw1pY=

Name: OSGI-INF/l10n/bundle.properties
SHA1-Digest: Lots54dgITVnDYNkKQ10QJWZVDo=

Name: org/apache/maven/index/UniqueArtifactFilterPostprocessor.class
SHA1-Digest: vFkQZ+9tYKN3hPesqxFtlOWxWoM=

Name: org/apache/maven/index/OneLineFragmenter.class
SHA1-Digest: abCWdLm6zZjd1/PXVfpEsOJt6sw=

Name: org/apache/maven/index/DefaultScanner$ScannerFileComparator.clas
 s
SHA1-Digest: 0rAoR2jxNWE67hY+oUbcA9o/62k=

Name: org/apache/maven/index/GroupedSearchRequest.class
SHA1-Digest: w24Gz+QFZ/WSSmwIxFkxepmu0uU=

Name: org/apache/maven/index/DefaultScanner$1.class
SHA1-Digest: gkH/dx2WnbEszT4gZZk+Xqun3zc=

Name: org/apache/maven/index/AbstractSearchResponse.class
SHA1-Digest: +9+LG5BPsucJ1z9/3hJouVOQLoM=

Name: org/apache/maven/index/SearchType$1.class
SHA1-Digest: IlOmZdPJP38sASjGYTwngSzOvns=

Name: org/apache/maven/index/ScanningRequest.class
SHA1-Digest: dkw3QtwIwZftkaIBlO9YIPdqzyI=

Name: org/apache/maven/index/DefaultScannerListener.class
SHA1-Digest: n5fSIz/YDz+MaGZroU8mVlDRUj0=

Name: org/apache/maven/index/DefaultNexusIndexer.class
SHA1-Digest: XmgrT+wwdT2oSsDV3Thg2IqsjDc=

Name: org/apache/maven/index/IndexerEngine.class
SHA1-Digest: p4t27yVfOI7+jOvuhcPu6hxAZdw=

Name: org/apache/maven/index/CleaningEncoder.class
SHA1-Digest: a1nkpDVlMzBDoyox+4Yx5CGG16g=

Name: org/apache/maven/index/DefaultSearchEngine.class
SHA1-Digest: 8SDiNnUb+nt+rkULQR5sVnAIt0I=

Name: org/apache/maven/index/DefaultIndexerEngine.class
SHA1-Digest: Ayhf9V++6vhG/u5+W9upwekm61g=

Name: org/apache/maven/index/IteratorSearchResponse$1.class
SHA1-Digest: dVfHQ3byjYbLq1ZEX/BF89dKdk4=

Name: org/apache/maven/index/IteratorResultSet.class
SHA1-Digest: vgk0/vpMNIAflHHz8o17IhvycYE=

Name: org/apache/maven/index/ArtifactInfo$VersionComparator.class
SHA1-Digest: ECwpjSzSJ2EWJkDmEZo5RNGw/3g=

Name: about.html
SHA1-Digest: WE2mgLguSznUrqkmfww8Xdq/9iY=

Name: org/apache/maven/index/Field.class
SHA1-Digest: u2R3WdH7b6lN8tfc+nNb83rTIOc=

Name: org/apache/maven/index/ScanningResult.class
SHA1-Digest: 62lKrIzEgdFl85nxOiRHn1qJR0Y=

Name: org/apache/maven/index/DefaultArtifactContextProducer.class
SHA1-Digest: vJ0a3Xiklp18I1K4cW+7tI1qLoA=

Name: org/apache/maven/index/MatchHighlight.class
SHA1-Digest: x04hI/XX5zk+JCUZupDkFR6bUL4=

Name: org/apache/maven/index/ArtifactContext.class
SHA1-Digest: ad0F5Zzg9uLRSdPCxYsY0zQuLjo=

Name: org/apache/maven/index/ArtifactInfoGroup.class
SHA1-Digest: 71A5hfzBsNu3VI52nSBPcob0nsc=

Name: org/apache/maven/index/MatchHighlightMode.class
SHA1-Digest: oS2ZXg/x3jS/gZ98riS4SFvXMBo=

Name: org/apache/maven/index/DefaultIteratorResultSet.class
SHA1-Digest: f/Kwz3Tze+E0C4ncSe5YYdHcVRE=

Name: org/apache/maven/index/ArtifactContext$ModelReader.class
SHA1-Digest: TDxs/E+36Va5xPplFUwI0ni3HyI=

Name: indexer-artifact-3.1.0.jar
SHA1-Digest: MNz7glWNxUUqwTXyZ0AeIUoOmFk=

Name: org/apache/maven/index/AbstractSearchRequest.class
SHA1-Digest: WVyRsaeWHYYKNlpGscltsVHEhDI=

Name: org/apache/maven/index/ArtifactInfoFilter.class
SHA1-Digest: iUovfvGAg1A+kxA+AFgvTS0tw6k=

Name: org/apache/maven/index/IndexerField.class
SHA1-Digest: VxkFY3Jj5UvctFRyPbu3J5fmxf4=

Name: org/apache/maven/index/DefaultScanner.class
SHA1-Digest: 6TxXfjxkvKYgRu4+jMUPMKNy9fw=

