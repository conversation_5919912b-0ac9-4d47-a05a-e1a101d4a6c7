P.Code {
	display: block;
	text-align: left;
	text-indent: 0.00pt;
	margin-top: 0.000000pt;
	margin-bottom: 0.000000pt;
	margin-right: 0.000000pt;
	margin-left: 1.5em;
	font-size: 100%;
	font-weight: medium;
	font-style: Regular;
	color: #4444CC;
	text-decoration: none;
	vertical-align: baseline;
	text-transform: none;
	font-family: "Courier New";
}
H6.CaptionFigColumn {
	display: block;
	text-align: left;
	text-indent: 0.000000pt;
	margin-top: 0.3em;
	margin-bottom: 1.1em;
	margin-right: 0.000000pt;
	margin-left: 0.000000pt;
	font-size: 90%;
	font-weight: medium;
	font-style: Italic;
	color: #000000;
	text-decoration: none;
	vertical-align: baseline;
	text-transform: none;
	font-family: "Arial";
}
P.Note {
	display: block;
	text-align: left;
	text-indent: 0pt;
	margin-top: 1.95em;
	margin-bottom: 1.95em;
	margin-right: 0.000000pt;
	margin-left: 3.0em;
	font-size: 110%;
	font-weight: medium;
	font-style: Italic;
	color: #000000;
	text-decoration: none;
	vertical-align: baseline;
	text-transform: none;
	font-family: "Arial";
}
EM.UILabel {
	font-weight: Bold;
	font-style: Regular;
	text-decoration: none;
	vertical-align: baseline;
	text-transform: none;
}
EM.CodeName {
	font-weight: Bold;
	font-style: Regular;
	text-decoration: none;
	vertical-align: baseline;
	text-transform: none;
	font-family:"Courier New";
}



body, html { border: 0px }

/* following font face declarations need to be removed for DBCS */

body, h1, h2, h3, h4, h5, h6, p, table, td, caption, th, ul, ol, dl, li, dd, dt {font: message-box; color: #000000}
pre				{ font-family: Courier, monospace}

/* end font face declarations */

/* following font size declarations should be OK for DBCS */
body, h1, h2, h3, h4, h5, h6, p, table, td, caption, th, ul, ol, dl, li, dd, dt {font: message-box; }
pre				{ font-size: 100% }
code,samp		{ font-size: 100%; }

/* end font size declarations */

body	     { background: #FFFFFF}
h1           { font-size: 180%; font-weight: medium; margin-top: 0.28em; margin-bottom: 0.05em; color: Highlight }
h2           { font-size: 140%; font-weight: bold; margin-top: 0.22em; margin-bottom: 3; color: Highlight }
h3           { font-size: 110%; font-weight: bold; margin-top: 0.18em; margin-bottom: 3 }
h4           { font-size: 100%; font-weight: bold; margin-top: 0.2em; margin-bottom: 3; font-style: italic }
p            { margin-top: 1.0em; margin-bottom: 1.0em }
pre	     { margin-left: 6; font-size: 90% }
a:link	     { color: #0000FF }
a:hover	     { color: #000080 }
a:visited    { text-decoration: underline }
ul	     { margin-top: 0; 
           margin-bottom: 1.0em; 
           margin-left : 1.0em;
           padding-left: 0;
           }
li	     { margin-top: 0; 
           margin-bottom: 0;
           padding-left: 0;
           margin-left: 0; 
         } 
li p	     { margin-top: 0; margin-bottom: 0 } 
ol	     { margin-top: 0; 
           margin-bottom: 10;
           padding-left: 0;
           margin-left: 1.4em }
dl	     { margin-top: 0; margin-bottom: 10 }
dt	     { margin-top: 0; margin-bottom: 0; font-weight: bold }
dd	     { margin-top: 0; margin-bottom: 0 }
strong	     { font-weight: bold}
em	     { font-style: italic}
var	     { font-style: italic}
div.revision { border-left-style: solid; border-left-width: thin; 
				   border-left-color: #7B68EE; padding-left:5 }
th	     { font-weight: bold }
