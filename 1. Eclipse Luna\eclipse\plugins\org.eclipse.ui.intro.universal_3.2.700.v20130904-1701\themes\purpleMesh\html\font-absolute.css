/*******************************************************************************
 * Copyright (c) 2008 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials 
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * 
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *******************************************************************************/

.intro-header H1 {
	font-size : 16pt;
}

h2 {
	font-size : 13pt;
}

/* For regular div labels */
H4 .div-label {
	font-size : 10pt;
}

/* The label part of the folding section */
.section-title-link .section-title {
	font-size : 10pt;
}

/* For the main page content's title */
#content-header H4 .div-label {
	font-size : 14pt;
}

.page-description { 
	font-size : 10pt;
}

a .link-label {
	font-size : 10pt;
}

#navigation-links a .link-label {
	font-size : 9pt;
}

a .text {
	font-size : 8pt;
}

p .group-description {
	font-size : 10pt;
}

.categoryContentnav {
	font-size: 9pt; 
}

.topicList {
	font-size: 8pt; 
}

/* 
 * Set up general font colours, sizes, etc.  Some of these will override
 * settings from the shared CSS 
 */
#root .intro-header H1 {
	font-size : 18pt;
}
#root #page-links a .link-label, #action-links a .link-label {
	font-size : 13pt;
}

#root #page-links a p .text, #action-links a p .text {
	font-size : 13pt;
}

#standby .intro-header H1 {
	font-size : 15pt;
}

#standby #page-links a .link-label, #standby #action-links a .link-label {
	font-size : 10pt;
}

#standby #page-links a p .text, #standby #action-links a p .text {
	font-size : 10pt;
}
