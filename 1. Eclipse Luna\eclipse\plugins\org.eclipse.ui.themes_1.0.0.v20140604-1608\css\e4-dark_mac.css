/*******************************************************************************
 * Copyright (c) 2010, 2014 <PERSON> and others.
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *     <PERSON> <<EMAIL>> - initial API and implementation
 *******************************************************************************/

/*******************************************************************************
 * The following bugs are referred to in this style sheet
 * 1.) Bug 419482 - Cascading policy in CSS
 * 2.) Bug 430052 - Imported rules cannot be overridden
 *******************************************************************************/


/* @import url("platform:/plugin/org.eclipse.ui.themes/css/e4-dark.css"); Bug 430052 */
@import url("platform:/plugin/org.eclipse.ui.themes/css/dark/e4-dark_basestyle.css");
@import url("platform:/plugin/org.eclipse.ui.themes/css/dark/e4-dark_globalstyle.css"); /* Remove this to have ONLY the main IDE shell dark */
@import url("platform:/plugin/org.eclipse.ui.themes/css/dark/e4-dark_partstyle.css");


.MTrimmedWindow.topLevel {
    margin-top: 3px;
    margin-bottom: 3px;
    margin-left: 3px;
    margin-right: 3px;
}

.MPartStack, .MPart {
    font-family: '#org-eclipse-ui-workbench-TAB_TEXT_FONT';
}

.MPartStack {
    swt-tab-renderer: url('bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.CTabRendering');
    swt-selected-tab-fill: '#org-eclipse-ui-workbench-INACTIVE_TAB_BG_START' '#org-eclipse-ui-workbench-INACTIVE_TAB_BG_END' 100%; /* title background for selected tab */
    swt-unselected-tabs-color: '#org-eclipse-ui-workbench-INACTIVE_UNSELECTED_TABS_COLOR_START' '#org-eclipse-ui-workbench-INACTIVE_UNSELECTED_TABS_COLOR_END' 100% 100%; /* title background for unselected tab */
    swt-outer-keyline-color: '#org-eclipse-ui-workbench-INACTIVE_TAB_OUTER_KEYLINE_COLOR'; /* border color for whole tabs container */
    swt-inner-keyline-color: '#org-eclipse-ui-workbench-INACTIVE_TAB_INNER_KEYLINE_COLOR';
    swt-tab-outline: '#org-eclipse-ui-workbench-INACTIVE_TAB_OUTLINE_COLOR'; /* border color for selected tab */
    padding: 0px 2px 2px;
    swt-shadow-visible: false;
    swt-mru-visible: true;
    swt-corner-radius: 16px;
}

.MPartStack.active {
    swt-selected-tab-fill: '#org-eclipse-ui-workbench-ACTIVE_TAB_BG_START' '#org-eclipse-ui-workbench-ACTIVE_TAB_BG_END' 100%; /* title background for selected tab */
    swt-unselected-tabs-color: '#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_START' '#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_END' 100% 100%; /* title background for unselected tab */
    swt-outer-keyline-color: '#org-eclipse-ui-workbench-ACTIVE_TAB_OUTER_KEYLINE_COLOR'; /* border color for whole tabs container */
    swt-inner-keyline-color: '#org-eclipse-ui-workbench-ACTIVE_TAB_INNER_KEYLINE_COLOR';
    swt-tab-outline: '#org-eclipse-ui-workbench-ACTIVE_TAB_OUTLINE_COLOR'; /* border color for selected tab */
}

.MPartStack.active.noFocus {
    swt-selected-tabs-fill: '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_BG_START' '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_BG_END' 100% 100%;
}

.MPartStack.empty {
    swt-unselected-tabs-color: '#org-eclipse-ui-workbench-INACTIVE_UNSELECTED_TABS_COLOR_START' #4F5456 #4F5456 99% 100%; /* title background for unselected tab */
    swt-tab-outline: #535354; /* border color for selected tab */
    swt-outer-keyline-color: #515658; /* border color for whole tabs container */
}

.MPart.busy {
    font-style: italic;
}

.MPart.highlighted {
    font-weight: bold;
}

CTabItem,
CTabItem CLabel {
    background-color: '#org-eclipse-ui-workbench-INACTIVE_TAB_BG_END'; /* HACK for background of CTabFolder inner Toolbars */
    color: '#org-eclipse-ui-workbench-INACTIVE_TAB_UNSELECTED_TEXT_COLOR';
}
CTabItem:selected,
CTabItem:selected CLabel {
    color: '#org-eclipse-ui-workbench-INACTIVE_TAB_SELECTED_TEXT_COLOR';
}

.MPartStack.active > CTabItem,
.MPartStack.active > CTabItem CLabel {
    background-color: '#org-eclipse-ui-workbench-ACTIVE_TAB_BG_END'; /* HACK for background of CTabFolder inner Toolbars */
    color: '#org-eclipse-ui-workbench-ACTIVE_TAB_UNSELECTED_TEXT_COLOR';
}
.MPartStack.active > CTabItem:selected,
.MPartStack.active > CTabItem:selected CLabel {
    color: '#org-eclipse-ui-workbench-ACTIVE_TAB_SELECTED_TEXT_COLOR';
}

.MPartStack.active.noFocus > CTabItem:selected {
    color: '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_SELECTED_TEXT_COLOR';
}

CTabItem.busy {
    color: #888888;
}

#PerspectiveSwitcher {
    eclipse-perspective-keyline-color: #AAB0BF #AAB0BF;
}

.MToolControl.TrimStack {
    /*frame-image:  url(./gtkTSFrame.png);*/
    handle-image:  url(./dragHandle.png);
    frame-cuts: 5px 1px 5px 16px;
}

.MToolBar.Draggable {
    handle-image:  url(./dragHandle.png);
}

.MToolControl.Draggable {
    handle-image:  url(./dragHandle.png);
}

.DragFeedback {
    background-color: COLOR-WIDGET-NORMAL-SHADOW;
}

.ModifiedDragFeedback {
    background-color: #4176AF;
}

.MTrimmedWindow {
    background-color: #515658;
}

.MTrimBar {
    background-color: #515658;
}

CTabFolder.MArea .MPartStack,CTabFolder.MArea .MPartStack.active {
    swt-shadow-visible: false;
}


CTabFolder Tree, CTabFolder Canvas {
    background-color: #2F2F2F;
    color: #CCC;
}
.MPartStack.active Tree,
.MPartStack.active CTabFolder Canvas {
    background-color: #262626;
    color: #CCC;
}

.MPartStack.active Table {
    background-color: #2F2F2F;
    color: #CCC;
}

.View {
    background-color: #313538;
    color: #F5F5F5;
}


/* ###################### Top Toolbar ########################## */

#org-eclipse-ui-main-toolbar, #PerspectiveSwitcher {
    eclipse-perspective-keyline-color: #585858;
    background-color: #515658 #515658 100%;
    handle-image: none;
    color: #EBE8E4;
}


/* #################### Bottom Status Bar ######################## */

#org-eclipse-ui-StatusLine,
#org-eclipse-ui-ProgressBar,
#org-eclipse-ui-ProgressBar Canvas {
    color: #CCCCCC;
}
#org-eclipse-ui-StatusLine CLabel {
    color: #BDBAB7;
}

StatusLine, ImageBasedFrame{
    color: #BDBAB7;
}



/* ###################### Global Styles ########################## */

TabFolder,
/* the following are required due to Bug 419482: */
TabFolder > Composite > TabFolder,
TabFolder > Composite > * > TabFolder,
DocCommentOwnerComposite > Group > TabFolder,
TabFolder > Composite > ScrolledComposite > TabFolder,
Shell > Composite > Composite > TabFolder,
Composite > Composite > Composite > Group > TabFolder,
Shell > Composite > Composite > Composite > TabFolder,
ScrolledComposite > Composite > Composite > Composite > TabFolder,
Shell > Composite > Composite > Composite > Composite > Composite > TabFolder,
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > TabFolder,
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > * > TabFolder {
    /* background-color is not applied to the whole button,
       but text color is changed, so it appear light on light */
    background-color: #515658;
    color: #222;
}

Button,
/* the following are required due to Bug 419482: */
Composite > Button,
TabFolder > Composite > Button,
TabFolder > Composite > * > Button,
DocCommentOwnerComposite > Group > Button,
TabFolder > Composite > ScrolledComposite > Button,
Shell > Composite > Composite > Button,
Composite > Composite > Composite > Group > Button,
Shell > Composite > Composite > Composite > Button,
ScrolledComposite > Composite > Composite > Composite > Button,
Shell > Composite > Composite > Composite > Composite > Composite > Button,
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > Button,
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > * > Button,
Form > LayoutComposite > LayoutComposite > Button,
.MPart Button,
.MPartStack.active .MPart Button,
.MPart LayoutComposite > * > LayoutComposite > Section > LayoutComposite > Button,
.MPartStack.active .MPart LayoutComposite > * > LayoutComposite > Section > LayoutComposite > Button {
    /* background-color is not applied to the whole button,
       but text color is changed, so it appear light on light */
    background-color: #515658;
    color: #222;
}
Button[style~='SWT.CHECK'],
/* the following are required due to Bug 419482: */
Composite > Button[style~='SWT.CHECK'],
TabFolder > Composite > Button[style~='SWT.CHECK'],
TabFolder > Composite > * > Button[style~='SWT.CHECK'],
DocCommentOwnerComposite > Group > Button[style~='SWT.CHECK'],
TabFolder > Composite > ScrolledComposite > Button[style~='SWT.CHECK'],
Shell > Composite > Composite > Button[style~='SWT.CHECK'],
Composite > Composite > Composite > Group > Button[style~='SWT.CHECK'],
Shell > Composite > Composite > Composite > Button[style~='SWT.CHECK'],
ScrolledComposite > Composite > Composite > Composite > Button[style~='SWT.CHECK'],
Shell > Composite > Composite > Composite > Composite > Composite > Button[style~='SWT.CHECK'],
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > Button[style~='SWT.CHECK'],
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > * > Button[style~='SWT.CHECK'],
Form > LayoutComposite > LayoutComposite > Button[style~='SWT.CHECK'],
.MPart Button[style~='SWT.CHECK'],
.MPartStack.active .MPart Button[style~='SWT.CHECK'],
.MPart LayoutComposite > * > LayoutComposite > Section > LayoutComposite > Button[style~='SWT.CHECK'],
.MPartStack.active .MPart LayoutComposite > * > LayoutComposite > Section > LayoutComposite > Button[style~='SWT.CHECK'] {
    /* currently, Button object isn't consistent (eg. also a checkbox is seen as Button) */
    /* so, css rules applied to Button have to be overridden for non-Button matches */
    color: #ddd;
}
Button[style~='SWT.RADIO'],
/* the following are required due to Bug 419482: */
Composite > Button[style~='SWT.RADIO'],
TabFolder > Composite > Button[style~='SWT.RADIO'],
TabFolder > Composite > * > Button[style~='SWT.RADIO'],
DocCommentOwnerComposite > Group > Button[style~='SWT.RADIO'],
TabFolder > Composite > ScrolledComposite > Button[style~='SWT.RADIO'],
Shell > Composite > Composite > Button[style~='SWT.RADIO'],
Composite > Composite > Composite > Group > Button[style~='SWT.RADIO'],
Shell > Composite > Composite > Composite > Button[style~='SWT.RADIO'],
ScrolledComposite > Composite > Composite > Composite > Button[style~='SWT.RADIO'],
Shell > Composite > Composite > Composite > Composite > Composite > Button[style~='SWT.RADIO'],
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > Button[style~='SWT.RADIO'],
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > * > Button[style~='SWT.RADIO'],
Form > LayoutComposite > LayoutComposite > Button[style~='SWT.RADIO'],
.MPart Button[style~='SWT.RADIO'],
.MPartStack.active .MPart Button[style~='SWT.RADIO'],
.MPart LayoutComposite > * > LayoutComposite > Section > LayoutComposite > Button[style~='SWT.RADIO'],
.MPartStack.active .MPart LayoutComposite > * > LayoutComposite > Section > LayoutComposite > Button[style~='SWT.RADIO'] {
    /* currently, Button object isn't consistent (eg. also a checkbox is seen as Button) */
    /* so, css rules applied to Button have to be overridden for non-Button matches */
    color: #ddd;
}

Combo,
/* the following are required due to Bug 419482: */
Composite > Combo,
TabFolder > Composite > Combo,
TabFolder > Composite > * > Combo,
DocCommentOwnerComposite > Group > Combo,
TabFolder > Composite > ScrolledComposite > Combo,
Shell > Composite > Composite > Combo,
Composite > Composite > Composite > Group > Combo,
Shell > Composite > Composite > Composite > Combo,
ScrolledComposite > Composite > Composite > Composite > Combo,
Shell > Composite > Composite > Composite > Composite > Composite > Combo,
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > Combo,
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > * > Combo,
Form > LayoutComposite > LayoutComposite > Combo,
.MPart LayoutComposite > * > LayoutComposite > Section > LayoutComposite > Combo {
    background-color: #949DA5; 
    color: #222;  /* background of drop-drown list is hard-coded to white */
}
Combo:selected,
/* the following are required due to Bug 419482: */
Composite > Combo:selected,
TabFolder > Composite > Combo:selected,
TabFolder > Composite > * > Combo:selected,
DocCommentOwnerComposite > Group > Combo:selected,
TabFolder > Composite > ScrolledComposite > Combo:selected,
Shell > Composite > Composite > Combo:selected,
Composite > Composite > Composite > Group > Combo:selected,
Shell > Composite > Composite > Composite > Combo:selected,
ScrolledComposite > Composite > Composite > Composite > Combo:selected,
Shell > Composite > Composite > Composite > Composite > Composite > Combo:selected,
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > Combo:selected,
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > * > Combo:selected,
Form > LayoutComposite > LayoutComposite > Combo:selected,
.MPart LayoutComposite > * > LayoutComposite > Section > LayoutComposite > Combo:selected {
    background-color: #41464A;
    color: #FFF;
}

Text[style~='SWT.SEARCH'],
/* the following are required due to Bug 419482: */
Composite > Text[style~='SWT.SEARCH'],
TabFolder > Composite > Text[style~='SWT.SEARCH'],
TabFolder > Composite > * > Text[style~='SWT.SEARCH'],
DocCommentOwnerComposite > Group > Text[style~='SWT.SEARCH'],
TabFolder > Composite > ScrolledComposite > Text[style~='SWT.SEARCH'],
Shell > Composite > Composite > Text[style~='SWT.SEARCH'],
Composite > Composite > Composite > Group > Text[style~='SWT.SEARCH'],
Shell > Composite > Composite > Composite > Text[style~='SWT.SEARCH'],
ScrolledComposite > Composite > Composite > Composite > Text[style~='SWT.SEARCH'],
Shell > Composite > Composite > Composite > Composite > Composite > Text[style~='SWT.SEARCH'],
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > Text[style~='SWT.SEARCH'],
Shell[style~='SWT.RADIO'][style~='SWT.CASCADE'][style~='SWT.SHADOW_ETCHED_IN'][style~='SWT.SHADOW_ETCHED_OUT'][style~='SWT.RESIZE'][style~='SWT.MENU'][style~='SWT.FULL_SELECTION'][style~='SWT.DATE'] > Composite > * > Text[style~='SWT.SEARCH'],
#org-eclipse-pde-runtime-LogView Text[style~='SWT.SEARCH'],
Form > LayoutComposite > LayoutComposite > Text[style~='SWT.SEARCH'],
.MPart LayoutComposite > * > LayoutComposite > Section > LayoutComposite > Text[style~='SWT.SEARCH'] {
    /* search boxes */
    background-color: #949da5; /* background-color is hard-coded */
    color: #333;
}
