##################################################################################################
# Copyright (c) 2005 Eteration A.S. and Gorkem Ercan. All rights reserved. This program and the
# accompanying materials are made available under the terms of the Eclipse Public License v1.0
# which accompanies this distribution, and is available at
# http://www.eclipse.org/legal/epl-v10.html
# 
# Contributors: Gorkem Ercan - initial API and implementation
#               
###################################################################################################
pluginName= JOnAS Generic Server definitions
providerName=Eclipse.org
pluginDescription=Provides server definitions for JOnAS server

# ============ jonas4 ====================
jonasCategory=ObjectWeb
jonas4runtimeTypeName=JOnAS v4
jonas4runtimeTypeDescription=Publishes and runs J2EE 1.4 modules on a local server. \
Provides basic server functionality

jonas4serverTypeName=JOnAS v4
jonas4serverTypeDescription=Publishes and runs J2EE 1.4 modules on a local server. \
Provides basic server functionality
# ============== serverdef translations ================
ApplicationServerDirectory=Application &Server Directory:
serverAddress=A&ddress:
serverPort=&Port:
jndiPort=&JNDI Port:
serverclassPath=&Classpath Variable:
jonasmappernames=&Mapper names:
jonasEjbProtocols=&Ejb Protocols:
jonasBase=JonAS Co&nfiguration Directory:
jonasRoot=JonAS &Installation Directory:
serverName=Ser&ver Name:
username=Use&r Name:
password=Pass&word:
