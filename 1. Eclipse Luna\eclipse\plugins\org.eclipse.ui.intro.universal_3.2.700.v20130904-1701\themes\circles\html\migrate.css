/*******************************************************************************
 * Copyright (c) 2006 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials 
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * 
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *******************************************************************************/

/* show the "selected" image for this page */
#navigation-links a#migrate img, 
#navigation-links a#migrate:hover img,
#navigation-links a#migrate:focus img,
#navigation-links a#migrate:active img {
	background-image : url(../graphics/icons/ctool/mi_nav_64.gif); 
	top : 2px;
	left : -3px;
}

#navigation-links {
	background-image: url(../graphics/contentpage/mi_banner.jpg);
}

#navigation-links a:hover#migrate .link-label,
#navigation-links a:focus#migrate .link-label,
#navigation-links a:active#migrate .link-label {
	display : none;
}

#navigation-links a:hover#migrate,
#navigation-links a:focus#migrate,
#navigation-links a:active#migrate {
	background-image : none;
}

#navigation-links a:hover#migrate .link-extra-div,
#navigation-links a:focus#migrate .link-extra-div,
#navigation-links a:active#migrate .link-extra-div {
	background-image : none;
}