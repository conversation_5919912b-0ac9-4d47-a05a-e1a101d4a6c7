Manifest-Version: 1.0
Bundle-DocURL: www.eclipse.org
Bundle-RequiredExecutionEnvironment: JavaSE-1.6,JavaSE-1.7
Built-By: ifedorenk
Bundle-SymbolicName: org.eclipse.m2e.maven.runtime;singleton:=false
Require-Bundle: org.slf4j.api;bundle-version="1.6.2",org.eclipse.m2e.m
 aven.runtime.slf4j.simple;bundle-version="[1.5.0,1.6.0)"
Export-Package: org.apache.maven,org.apache.maven.artifact,org.apache.
 maven.artifact.deployer,org.apache.maven.artifact.factory,org.apache.
 maven.artifact.handler,org.apache.maven.artifact.handler.manager,org.
 apache.maven.artifact.installer,org.apache.maven.artifact.manager,org
 .apache.maven.artifact.metadata,org.apache.maven.artifact.repository,
 org.apache.maven.artifact.repository.layout,org.apache.maven.artifact
 .repository.metadata,org.apache.maven.artifact.repository.metadata.io
 ,org.apache.maven.artifact.repository.metadata.io.xpp3,org.apache.mav
 en.artifact.resolver,org.apache.maven.artifact.resolver.filter,org.ap
 ache.maven.artifact.versioning,org.apache.maven.classrealm,org.apache
 .maven.cli,org.apache.maven.cli.event,org.apache.maven.cli.logging,or
 g.apache.maven.cli.logging.impl,org.apache.maven.cli.transfer,org.apa
 che.maven.configuration,org.apache.maven.configuration.internal,org.a
 pache.maven.eventspy,org.apache.maven.eventspy.internal,org.apache.ma
 ven.exception,org.apache.maven.execution,org.apache.maven.execution.s
 cope,org.apache.maven.execution.scope.internal,org.apache.maven.lifec
 ycle,org.apache.maven.lifecycle.internal,org.apache.maven.lifecycle.i
 nternal.builder,org.apache.maven.lifecycle.internal.builder.multithre
 aded,org.apache.maven.lifecycle.internal.builder.singlethreaded,org.a
 pache.maven.lifecycle.mapping,org.apache.maven.messages,org.apache.ma
 ven.model,org.apache.maven.model.building,org.apache.maven.model.comp
 osition,org.apache.maven.model.inheritance,org.apache.maven.model.int
 erpolation,org.apache.maven.model.io,org.apache.maven.model.io.xpp3,o
 rg.apache.maven.model.locator,org.apache.maven.model.management,org.a
 pache.maven.model.merge,org.apache.maven.model.normalization,org.apac
 he.maven.model.path,org.apache.maven.model.plugin,org.apache.maven.mo
 del.profile,org.apache.maven.model.profile.activation,org.apache.mave
 n.model.resolution,org.apache.maven.model.superpom,org.apache.maven.m
 odel.validation,org.apache.maven.monitor.event,org.apache.maven.monit
 or.logging,org.apache.maven.plugin,org.apache.maven.plugin.descriptor
 ,org.apache.maven.plugin.internal,org.apache.maven.plugin.lifecycle,o
 rg.apache.maven.plugin.lifecycle.io.xpp3,org.apache.maven.plugin.logg
 ing,org.apache.maven.plugin.prefix,org.apache.maven.plugin.prefix.int
 ernal,org.apache.maven.plugin.version,org.apache.maven.plugin.version
 .internal,org.apache.maven.profiles,org.apache.maven.profiles.activat
 ion,org.apache.maven.profiles.io.xpp3,org.apache.maven.project,org.ap
 ache.maven.project.artifact,org.apache.maven.project.inheritance,org.
 apache.maven.project.interpolation,org.apache.maven.project.path,org.
 apache.maven.project.validation,org.apache.maven.properties.internal,
 org.apache.maven.reporting,org.apache.maven.repository,org.apache.mav
 en.repository.internal,org.apache.maven.repository.legacy,org.apache.
 maven.repository.legacy.metadata,org.apache.maven.repository.legacy.r
 epository,org.apache.maven.repository.legacy.resolver,org.apache.mave
 n.repository.legacy.resolver.conflict,org.apache.maven.repository.leg
 acy.resolver.transform,org.apache.maven.repository.metadata,org.apach
 e.maven.rtinfo,org.apache.maven.rtinfo.internal,org.apache.maven.sett
 ings,org.apache.maven.settings.building,org.apache.maven.settings.cry
 pto,org.apache.maven.settings.io,org.apache.maven.settings.io.xpp3,or
 g.apache.maven.settings.merge,org.apache.maven.settings.validation,or
 g.apache.maven.toolchain,org.apache.maven.toolchain.java,org.apache.m
 aven.toolchain.model,org.apache.maven.toolchain.model.io.xpp3,org.apa
 che.maven.usability.plugin,org.apache.maven.usability.plugin.io.xpp3,
 org.apache.maven.wagon,org.apache.maven.wagon.authentication,org.apac
 he.maven.wagon.authorization,org.apache.maven.wagon.events,org.apache
 .maven.wagon.observers,org.apache.maven.wagon.providers.file,org.apac
 he.maven.wagon.proxy,org.apache.maven.wagon.repository,org.apache.mav
 en.wagon.resource,org.codehaus.plexus,org.codehaus.plexus.classworlds
 ,org.codehaus.plexus.classworlds.launcher,org.codehaus.plexus.classwo
 rlds.realm,org.codehaus.plexus.classworlds.strategy,org.codehaus.plex
 us.component,org.codehaus.plexus.component.annotations,org.codehaus.p
 lexus.component.composition,org.codehaus.plexus.component.configurato
 r,org.codehaus.plexus.component.configurator.converters,org.codehaus.
 plexus.component.configurator.converters.basic,org.codehaus.plexus.co
 mponent.configurator.converters.composite,org.codehaus.plexus.compone
 nt.configurator.converters.lookup,org.codehaus.plexus.component.confi
 gurator.converters.special,org.codehaus.plexus.component.configurator
 .expression,org.codehaus.plexus.component.factory,org.codehaus.plexus
 .component.repository,org.codehaus.plexus.component.repository.except
 ion,org.codehaus.plexus.configuration,org.codehaus.plexus.configurati
 on.xml,org.codehaus.plexus.context,org.codehaus.plexus.interpolation,
 org.codehaus.plexus.interpolation.multi,org.codehaus.plexus.interpola
 tion.object,org.codehaus.plexus.interpolation.os,org.codehaus.plexus.
 interpolation.reflection,org.codehaus.plexus.interpolation.util,org.c
 odehaus.plexus.logging,org.codehaus.plexus.logging.console,org.codeha
 us.plexus.personality.plexus.lifecycle.phase,org.codehaus.plexus.util
 ,org.codehaus.plexus.util.cli,org.codehaus.plexus.util.cli.shell,org.
 codehaus.plexus.util.dag,org.codehaus.plexus.util.introspection,org.c
 odehaus.plexus.util.io,org.codehaus.plexus.util.reflection,org.codeha
 us.plexus.util.xml,org.codehaus.plexus.util.xml.pull,org.sonatype.ple
 xus.build.incremental,org.sonatype.plexus.components.cipher,org.sonat
 ype.plexus.components.sec.dispatcher,org.sonatype.plexus.components.s
 ec.dispatcher.model,org.sonatype.plexus.components.sec.dispatcher.mod
 el.io.xpp3,org.eclipse.aether;version="0.9.0.M2",org.eclipse.aether.a
 rtifact;version="0.9.0.M2",org.eclipse.aether.collection;version="0.9
 .0.M2",org.eclipse.aether.connector.wagon;version="0.9.0.M2",org.ecli
 pse.aether.deployment;version="0.9.0.M2",org.eclipse.aether.graph;ver
 sion="0.9.0.M2",org.eclipse.aether.impl;version="0.9.0.M2",org.eclips
 e.aether.installation;version="0.9.0.M2",org.eclipse.aether.internal.
 connector.wagon,org.eclipse.aether.internal.impl,org.eclipse.aether.m
 etadata;version="0.9.0.M2",org.eclipse.aether.repository;version="0.9
 .0.M2",org.eclipse.aether.resolution;version="0.9.0.M2",org.eclipse.a
 ether.spi.connector;version="0.9.0.M2",org.eclipse.aether.spi.io;vers
 ion="0.9.0.M2",org.eclipse.aether.spi.localrepo;version="0.9.0.M2",or
 g.eclipse.aether.spi.locator;version="0.9.0.M2",org.eclipse.aether.sp
 i.log;version="0.9.0.M2",org.eclipse.aether.transfer;version="0.9.0.M
 2",org.eclipse.aether.util;version="0.9.0.M2",org.eclipse.aether.util
 .artifact;version="0.9.0.M2",org.eclipse.aether.util.concurrency;vers
 ion="0.9.0.M2",org.eclipse.aether.util.filter;version="0.9.0.M2",org.
 eclipse.aether.util.graph.manager;version="0.9.0.M2",org.eclipse.aeth
 er.util.graph.selector;version="0.9.0.M2",org.eclipse.aether.util.gra
 ph.transformer;version="0.9.0.M2",org.eclipse.aether.util.graph.trave
 rser;version="0.9.0.M2",org.eclipse.aether.util.graph.visitor;version
 ="0.9.0.M2",org.eclipse.aether.util.listener;version="0.9.0.M2",org.e
 clipse.aether.util.repository;version="0.9.0.M2",org.eclipse.aether.u
 til.repository.layout;version="0.9.0.M2",org.eclipse.aether.util.vers
 ion;version="0.9.0.M2",org.eclipse.aether.version;version="0.9.0.M2",
 com.google.inject;version="1.4",com.google.inject.binder;version="1.4
 ",com.google.inject.internal,com.google.inject.internal.util,com.goog
 le.inject.matcher;version="1.4",com.google.inject.name;version="1.4",
 com.google.inject.spi;version="1.4",com.google.inject.util;version="1
 .4",javax.inject,com.squareup.okhttp,com.squareup.okhttp.internal,com
 .squareup.okhttp.internal.http,com.squareup.okhttp.internal.okio,com.
 squareup.okhttp.internal.spdy,com.squareup.okhttp.internal.tls,io.tes
 la.aether.client,io.tesla.aether.connector,io.tesla.aether.okhttp,io.
 tesla.aether.okhttp.ssl,io.tesla.aether.wagon,io.takari.aether.concur
 rency,io.takari.filemanager,io.takari.filemanager.internal,META-INF.p
 lexus,META-INF.sisu
Bundle-Version: 1.5.0.20140605-2032
Build-Jdk: 1.6.0_27
Bundle-ClassPath: .,jars/takari-local-repository-0.9.0.jar,jars/takari
 -filemanager-0.7.0.jar,jars/maven-core-3.2.1.jar,jars/maven-model-3.2
 .1.jar,jars/maven-settings-3.2.1.jar,jars/maven-settings-builder-3.2.
 1.jar,jars/maven-repository-metadata-3.2.1.jar,jars/maven-artifact-3.
 2.1.jar,jars/maven-plugin-api-3.2.1.jar,jars/maven-model-builder-3.2.
 1.jar,jars/maven-aether-provider-3.2.1.jar,jars/aether-api-0.9.0.M2.j
 ar,jars/aether-util-0.9.0.M2.jar,jars/plexus-interpolation-1.19.jar,j
 ars/plexus-utils-3.0.17.jar,jars/plexus-classworlds-2.5.1.jar,jars/pl
 exus-component-annotations-1.5.5.jar,jars/plexus-sec-dispatcher-1.3.j
 ar,jars/maven-embedder-3.2.1.jar,jars/plexus-cipher-1.7.jar,jars/comm
 ons-cli-1.2.jar,jars/maven-compat-3.2.1.jar,jars/wagon-provider-api-2
 .6.jar,jars/org.eclipse.sisu.plexus-0.0.0.M5.jar,jars/cdi-api-1.0.jar
 ,jars/jsr250-api-1.0.jar,jars/org.eclipse.sisu.inject-0.0.0.M5.jar,ja
 rs/sisu-guice-3.1.3-no_aop.jar,jars/javax.inject-1.jar,jars/guava-14.
 0.1.jar,jars/plexus-build-api-0.0.7.jar,jars/aether-impl-0.9.0.M2.jar
 ,jars/aether-spi-0.9.0.M2.jar,jars/aether-connector-okhttp-0.12.0.jar
 ,jars/okhttp-1.5.4.jar,jars/aether-connector-wagon-0.9.0.M2.jar,jars/
 wagon-file-2.6.jar,jars/commons-lang-2.6.jar
Bundle-Vendor: %Bundle-Vendor
Bnd-LastModified: 1402014750606
Bundle-Name: %Bundle-Name
Tool: Bnd-2.1.0.20130426-122213
Embed-Transitive: true
Embed-Directory: jars
Eclipse-BundleShape: dir
Created-By: Apache Maven Bundle Plugin
Embedded-Artifacts: jars/takari-local-repository-0.9.0.jar;g="io.takar
 i.aether";a="takari-local-repository";v="0.9.0",jars/takari-filemanag
 er-0.7.0.jar;g="io.takari";a="takari-filemanager";v="0.7.0",jars/mave
 n-core-3.2.1.jar;g="org.apache.maven";a="maven-core";v="3.2.1",jars/m
 aven-model-3.2.1.jar;g="org.apache.maven";a="maven-model";v="3.2.1",j
 ars/maven-settings-3.2.1.jar;g="org.apache.maven";a="maven-settings";
 v="3.2.1",jars/maven-settings-builder-3.2.1.jar;g="org.apache.maven";
 a="maven-settings-builder";v="3.2.1",jars/maven-repository-metadata-3
 .2.1.jar;g="org.apache.maven";a="maven-repository-metadata";v="3.2.1"
 ,jars/maven-artifact-3.2.1.jar;g="org.apache.maven";a="maven-artifact
 ";v="3.2.1",jars/maven-plugin-api-3.2.1.jar;g="org.apache.maven";a="m
 aven-plugin-api";v="3.2.1",jars/maven-model-builder-3.2.1.jar;g="org.
 apache.maven";a="maven-model-builder";v="3.2.1",jars/maven-aether-pro
 vider-3.2.1.jar;g="org.apache.maven";a="maven-aether-provider";v="3.2
 .1",jars/aether-api-0.9.0.M2.jar;g="org.eclipse.aether";a="aether-api
 ";v="0.9.0.M2",jars/aether-util-0.9.0.M2.jar;g="org.eclipse.aether";a
 ="aether-util";v="0.9.0.M2",jars/plexus-interpolation-1.19.jar;g="org
 .codehaus.plexus";a="plexus-interpolation";v="1.19",jars/plexus-utils
 -3.0.17.jar;g="org.codehaus.plexus";a="plexus-utils";v="3.0.17",jars/
 plexus-classworlds-2.5.1.jar;g="org.codehaus.plexus";a="plexus-classw
 orlds";v="2.5.1",jars/plexus-component-annotations-1.5.5.jar;g="org.c
 odehaus.plexus";a="plexus-component-annotations";v="1.5.5",jars/plexu
 s-sec-dispatcher-1.3.jar;g="org.sonatype.plexus";a="plexus-sec-dispat
 cher";v="1.3",jars/maven-embedder-3.2.1.jar;g="org.apache.maven";a="m
 aven-embedder";v="3.2.1",jars/plexus-cipher-1.7.jar;g="org.sonatype.p
 lexus";a="plexus-cipher";v="1.7",jars/commons-cli-1.2.jar;g="commons-
 cli";a="commons-cli";v="1.2",jars/maven-compat-3.2.1.jar;g="org.apach
 e.maven";a="maven-compat";v="3.2.1",jars/wagon-provider-api-2.6.jar;g
 ="org.apache.maven.wagon";a="wagon-provider-api";v="2.6",jars/org.ecl
 ipse.sisu.plexus-0.0.0.M5.jar;g="org.eclipse.sisu";a="org.eclipse.sis
 u.plexus";v="0.0.0.M5",jars/cdi-api-1.0.jar;g="javax.enterprise";a="c
 di-api";v="1.0",jars/jsr250-api-1.0.jar;g="javax.annotation";a="jsr25
 0-api";v="1.0",jars/org.eclipse.sisu.inject-0.0.0.M5.jar;g="org.eclip
 se.sisu";a="org.eclipse.sisu.inject";v="0.0.0.M5",jars/sisu-guice-3.1
 .3-no_aop.jar;g="org.sonatype.sisu";a="sisu-guice";v="3.1.3";c="no_ao
 p",jars/javax.inject-1.jar;g="javax.inject";a="javax.inject";v="1",ja
 rs/guava-14.0.1.jar;g="com.google.guava";a="guava";v="14.0.1",jars/pl
 exus-build-api-0.0.7.jar;g="org.sonatype.plexus";a="plexus-build-api"
 ;v="0.0.7",jars/aether-impl-0.9.0.M2.jar;g="org.eclipse.aether";a="ae
 ther-impl";v="0.9.0.M2",jars/aether-spi-0.9.0.M2.jar;g="org.eclipse.a
 ether";a="aether-spi";v="0.9.0.M2",jars/aether-connector-okhttp-0.12.
 0.jar;g="io.takari.aether";a="aether-connector-okhttp";v="0.12.0",jar
 s/okhttp-1.5.4.jar;g="com.squareup.okhttp";a="okhttp";v="1.5.4",jars/
 aether-connector-wagon-0.9.0.M2.jar;g="org.eclipse.aether";a="aether-
 connector-wagon";v="0.9.0.M2",jars/wagon-file-2.6.jar;g="org.apache.m
 aven.wagon";a="wagon-file";v="2.6",jars/commons-lang-2.6.jar;g="commo
 ns-lang";a="commons-lang";v="2.6"
Bundle-ManifestVersion: 2
Embed-Dependency: *;scope=compile|runtime;artifactId=!aopalliance|apac
 he-maven|slf4j-api

Name: jars/wagon-provider-api-2.6.jar
SHA1-Digest: YtpRk7uMzKDAvLoIaNcC4i8mszo=

Name: about_files/LICENSE-2.0.txt
SHA1-Digest: K4uBUimqimHkg/tLoFiLi2xJGJA=

Name: jars/org.eclipse.sisu.plexus-0.0.0.M5.jar
SHA1-Digest: iBjMWk0q/eL7yHwqeM8ev1cZqGk=

Name: jars/maven-settings-builder-3.2.1.jar
SHA1-Digest: tIxPfN9GtS+uFqgZveH8TS7jfQs=

Name: jars/commons-lang-2.6.jar
SHA1-Digest: DOHtuRTJTrw4jwhsaCfove7HGsI=

Name: jars/aether-connector-okhttp-0.12.0.jar
SHA1-Digest: HV0pfO3HIp/9AIwvxpnbVPvGvBQ=

Name: jars/aether-api-0.9.0.M2.jar
SHA1-Digest: l2YsmZxrL78u5Q6BSjRjnBwdIt4=

Name: OSGI-INF/l10n/bundle.properties
SHA1-Digest: dx5SI/bUcxCQDeuUDZ4e6OO/TPQ=

Name: jars/plexus-build-api-0.0.7.jar
SHA1-Digest: 5rpc1L/Y3gAjWvk25/Y+sk7UNuY=

Name: jars/plexus-cipher-1.7.jar
SHA1-Digest: UUYECbbNwrgoVAwZwFaR+JFB7cI=

Name: jars/sisu-guice-3.1.3-no_aop.jar
SHA1-Digest: cjuloO5jaJmK6wBj+SxlxBRlmKE=

Name: jars/maven-plugin-api-3.2.1.jar
SHA1-Digest: MFzqTpqJsSwIAI/qB5/Oot2jTso=

Name: jars/maven-repository-metadata-3.2.1.jar
SHA1-Digest: 4XGJi5+mFEnhyj7qRcBx/YLEh4k=

Name: jars/maven-compat-3.2.1.jar
SHA1-Digest: Z8yMxUN2KERfky0TXCZMumo6p8k=

Name: jars/guava-14.0.1.jar
SHA1-Digest: aeEvTGrqw5JVXx6ob6uCteXjGtQ=

Name: jars/aether-util-0.9.0.M2.jar
SHA1-Digest: uVcInetlRkfaMgrXUHsKS1ziOBM=

Name: jars/plexus-sec-dispatcher-1.3.jar
SHA1-Digest: 3twCA0+4/NdhXWZZMijLcXCRNLQ=

Name: jars/plexus-interpolation-1.19.jar
SHA1-Digest: smDKeiO7DSCXcdt6rjUEmJlDP+M=

Name: jars/takari-local-repository-0.9.0.jar
SHA1-Digest: oDm4uYWI9X5f3Eqc49L2hKUb0Cc=

Name: jars/aether-connector-wagon-0.9.0.M2.jar
SHA1-Digest: 0CZgWXWr53e66pXcDcv/FyPAgpo=

Name: jars/maven-artifact-3.2.1.jar
SHA1-Digest: In4MDTubJd2fzgp0uI7CEgJqD+I=

Name: jars/plexus-component-annotations-1.5.5.jar
SHA1-Digest: xy8mYNDL7SQkbdtV1/3E9zdNIHg=

Name: jars/maven-model-3.2.1.jar
SHA1-Digest: Moo8XPr5soMubh0hx2/O/gHgH94=

Name: jars/maven-embedder-3.2.1.jar
SHA1-Digest: fbyNHydcP/YpgAglvJurY9Np24o=

Name: jars/plexus-classworlds-2.5.1.jar
SHA1-Digest: mP6o6MP7DoZwpprW6kRYcsmXKRA=

Name: about.html
SHA1-Digest: XHLdptetnC+mk0OD48I3SicDOq4=

Name: jars/takari-filemanager-0.7.0.jar
SHA1-Digest: DHVbONaMMAK+NOBqL+1jvgeb9E8=

Name: jars/plexus-utils-3.0.17.jar
SHA1-Digest: e4b3pM7/yOrbsahCBxNK93b3y5U=

Name: jars/cdi-api-1.0.jar
SHA1-Digest: RMRT9gkJ38IjVSrOY+BcaUIVFWs=

Name: jars/javax.inject-1.jar
SHA1-Digest: aXXaOacEAle9UdIaIxt2yRWHLTg=

Name: jars/maven-core-3.2.1.jar
SHA1-Digest: +FCkTuSaJGfOWz5w+JHNvibVnDA=

Name: jars/aether-spi-0.9.0.M2.jar
SHA1-Digest: NkfQBiCpE2CZDJaA8p+8wi1pwu4=

Name: jars/maven-settings-3.2.1.jar
SHA1-Digest: uOnbWG+LLvuUpi3K14kjp+k5X0A=

Name: jars/maven-model-builder-3.2.1.jar
SHA1-Digest: SGEeCPjY6Wfm31kcjrnJ66Mpsfw=

Name: jars/okhttp-1.5.4.jar
SHA1-Digest: e3yeWmoNaaFXGWiAXcHR8ZtX2iY=

Name: jars/commons-cli-1.2.jar
SHA1-Digest: K/lreqi2EcF30ylFKvHckz4UUBw=

Name: jars/maven-aether-provider-3.2.1.jar
SHA1-Digest: q1fK/sTpwmUN+WPYPLFz0xH8rfA=

Name: jars/jsr250-api-1.0.jar
SHA1-Digest: UCVCJ2dzKhq0XZOr/qhGUT10Lc8=

Name: jars/org.eclipse.sisu.inject-0.0.0.M5.jar
SHA1-Digest: T2vaP1KMYKEucNsuej/u5TncyM0=

Name: jars/wagon-file-2.6.jar
SHA1-Digest: sNWlLbOwjiR9rRb3bbqg/Lbih00=

Name: jars/aether-impl-0.9.0.M2.jar
SHA1-Digest: 6rmkuq6N6WokwEIZI2Nj0Mpz6Kk=

