<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>How to use the XPath 2.0 grammar with PsychoPath</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch02.html" title="How to feed Psychopath XPath expressions"><link rel="prev" href="ch02s02.html" title="Schema Aware"><link rel="next" href="ch02s03s02.html" title="Path expressions"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="How to use the XPath 2.0 grammar with PsychoPath"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="How_to_use_the_XPath_2.0_grammar_with_PsychoPath"></a>How to use the XPath 2.0 grammar with PsychoPath</h2></div></div></div><p>In this section we will try to give you an overview of the XPath 2.0 grammar in general and how each production in the grammar should be used with PsychoPath. For the formal specifications, see the W3C web-site for XPath 2.0 specification 
				<a class="ulink" href="http://www.w3.org/TR/xpath20" target="_top">http://www.w3.org/TR/xpath20</a>.
				 
			</p><div class="section" title="Constants"><div class="titlepage"><div><div><h3 class="title"><a name="Constants"></a>Constants</h3></div></div></div><p>String literals are written as &ldquo;Hello&rdquo; or &lsquo;Hello&rsquo;. In each case the opposite kind of quotation mark can be used within the string: &lsquo;He said &ldquo;Hello&rdquo; &rsquo; or &ldquo;London is a big city&rdquo;. To feed PsychoPath, &ldquo; &lsquo;Hello World!&rsquo; &rdquo;or &ldquo; &ldquo;Hello World!&rdquo; &rdquo; can be used to feed it with strings. Remember that the ResultSequence returns AnyType so since a string is being expected as the result, first it has to be casted in the code like this: 
					String xsstring = (String)(rs.firstValue()); 
					Numeric constants follow the Java rules for decimal literals: for example, 4 or 4.67; a negative number can be written as -3.05. The numeric literal is taken as a double precision floating point number if it uses scientific notation (e.g. 1.0e7), as a fixed point decimal if it includes a decimal point, or as an integer otherwise. When extracting number literals from the ResultSequence, possible types to be returned include 
					<span class="italic">BigDecima''l (e.g.&nbsp;: xs:decimal: 4.67),''Int ''(e.g.&nbsp;: xs:integer: 4) or ''XSDouble</span> (e.g.&nbsp;: xs:double 1e0). All of which need to be casted in the same manner as stated before: from AnyType to their corresponding types. 
				</p><p>There are no boolean constants as such: 
					<span class="italic">true, false</span> instead the function calls 
					<span class="bold"><strong>true()</strong></span> and 
					<span class="bold"><strong>false()</strong></span> are used. 
				</p><p>Constants of other data types can be written using constructors. These look like function calls but require a string literal as their argument. For example, 
					<span class="bold"><strong>xs:float(&ldquo;10.7&rdquo;)</strong></span> produces a single-precision floating point number.
					 
				</p></div></div></body></html>