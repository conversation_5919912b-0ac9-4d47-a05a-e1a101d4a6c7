# plugin_customization.ini 
# sets default values for plug-in-specific preferences
# keys are qualified by plug-in id
# e.g., com.example.acmeplugin/myproperty=myvalue
# java.io.Properties file (ISO 8859-1 with "\" escapes)
# "%key" are externalized strings defined in plugin_customization.properties
# This file does not need to be translated.

# WARNING: This file defines the preference overrides for the Platform build 
# (the one with no JDT or PDE), not the SDK build (aka the IDE).  
# For the SDK build, use the plugin_customization.ini file in the 
# org.eclipse.sdk plug-in instead.

# Property "org.eclipse.ui/defaultPerspectiveId" controls the 
# perspective that the workbench opens initially
org.eclipse.ui/defaultPerspectiveId=org.eclipse.ui.resourcePerspective

# new-style tabs by default
org.eclipse.ui/SHOW_TRADITIONAL_STYLE_TABS=false

# put the perspective switcher on the top right
org.eclipse.ui/DOCK_PERSPECTIVE_BAR=topRight

# show progress on startup
org.eclipse.ui/SHOW_PROGRESS_ON_STARTUP=true

# Welcome theme to use
org.eclipse.ui.intro/INTRO_THEME = org.eclipse.ui.intro.universal.slate

# Root page links to show in the Universal Welcome
org.eclipse.ui.intro.universal/INTRO_ROOT_PAGES = overview,tutorials,samples,whatsnew,migrate

# Initial page layout of the Universal Welcome
org.eclipse.ui.intro.universal/INTRO_DATA = product:introData.xml

# Order help books in table of contents
org.eclipse.help/HELP_DATA = helpData.xml