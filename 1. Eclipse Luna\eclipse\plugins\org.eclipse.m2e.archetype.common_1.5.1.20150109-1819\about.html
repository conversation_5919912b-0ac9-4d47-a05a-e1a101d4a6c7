<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<title>About</title>
</head>
<body lang="EN-US">
<h2>About This Content</h2>
 
<p>June, 6 2011</p>	
<h3>License</h3>

<p>The Eclipse Foundation makes available all content in this plug-in (&quot;Content&quot;).  Unless otherwise 
indicated below, the Content is provided to you under the terms and conditions of the
Eclipse Public License Version 1.0 (&quot;EPL&quot;).  A copy of the EPL is available 
at <a href="http://www.eclipse.org/legal/epl-v10.html">http://www.eclipse.org/legal/epl-v10.html</a>.
For purposes of the EPL, &quot;Program&quot; will mean the Content.</p>

<p>If you did not receive this Content directly from the Eclipse Foundation, the Content is 
being redistributed by another party (&quot;Redistributor&quot;) and different terms and conditions may
apply to your use of any object code in the Content.  Check the Redistributor's license that was 
provided with the Content.  If no such license exists, contact the Redistributor.  Unless otherwise
indicated below, the terms and conditions of the EPL still apply to any source code in the Content
and such source code may be obtained at <a href="http://www.eclipse.org">http://www.eclipse.org</a>.</p>

<h3>Third Party Content</h3>

<p>
The Content includes items that have been sourced from third parties as set out below. If you 
did not receive this Content directly from the Eclipse Foundation, the following is provided 
for informational purposes only, and you should look to the Redistributor&rsquo;s license for 
terms and conditions of use.
</p>

<h4>Archetype 2.0-alpha-4</h4>
<p>
The plug-in includes software developed by The Apache Software Foundation as part of the Maven project.
Your use of Archetype 2.0-alpha-4 in binary code form contained in the plug-in is subject to the terms and conditions of the 
The Apache Software License, Version 2.0 (&quot;ASL&quot;).  
A copy of the ASL is available at <a href="http://maven.apache.org/license.html">http://maven.apache.org/license.html</a>.
(a local copy can be found <a href="about_files/LICENSE-2.0.txt">here</a>)
</p>
<p>The original binaries are available at the <a href="http://maven.org">Maven Central Repository</a>.</p>


<h4>Apache Commons Collections 3.2, Apache Commons I/O 1.3.2, Apache Commons Lang 2.1</h4>
<p>
The plug-in includes software developed by The Apache Software Foundation as part of the Apache Commons project.
Your use of Apache Commons Collections 3.2, Apache Commons I/O 1.3.2, Apache Commons Lang 2.1 in binary code form contained in the plug-in is subject to the terms and conditions of the 
The Apache Software License, Version 2.0 (&quot;ASL&quot;).  
A copy of the ASL is available at <a href="http://www.apache.org/licenses/LICENSE-2.0">http://www.apache.org/licenses/LICENSE-2.0</a>.
(a local copy can be found <a href="about_files/LICENSE-2.0.txt">here</a>)
</p>
<p>The original binaries are available at the <a href="http://maven.org">Maven Central Repository</a>.</p>
                                                                                                                


<h4>dom4j 1.6.1</h4>
<p>
Your use of dom4j 1.6.1 in binary code form contained in the plug-in is subject to the terms and conditions of 
BSD style license (&quot;BSD&quot;).  
A local copy of the license can be found <a href="about_files/dom4j-LICENSE.txt ">here</a>.
</p>
<p>The original binaries are available at the <a href="http://maven.org">Maven Central Repository</a>.</p>


<h4>jchardet 1.0</h4>
<p>
Your use of jchardet 1.0 in binary code form contained in the plug-in is subject to the terms and conditions of the 
Mozilla Public License 1.1 (&quot;MPL&quot;).  
A copy of the ASL is available at <a href="http://www.mozilla.org/MPL/MPL-1.1.html">http://www.mozilla.org/MPL/MPL-1.1.html</a>.
(a local copy can be found <a href="about_files/MPL-1.1.txt">here</a>)
</p>
<p>The original binaries are available at the <a href="http://maven.org">Maven Central Repository</a>.</p>


<h4>JDOM 1.0</h4>                                                                                                                                                    
<p>JDOM is available under an Apache-style open source license, with the acknowledgment clause removed..                                                             
This license is among the least restrictive license available, enabling developers to use JDOM in.                                                                   
creating new products without requiring them to release their own products as open source..                                                                          
This is the license model used by the Apache Project, which created the Apache server..                                                                              
A copy of the license is contained in the file <a href="about_files/LICENSE.txt">jdom-LICENSE.txt</a>.                                                                
</p>



<h4>Jakarta-Oro 2.0.8</h4>                                                                                                                                           
<p>The plug-in includes Jakarta-Oro 2.0.8 (&quot;Jakarta-Oro&quot;) developed by the Apache Software Foundation as part of the Jakarta project.                      
Your use of Jakarta-Oro 2.0.8 in binary code form contained in the plug-in is subject to the terms and conditions of the 
The Apache Software License, Version 1.1 (&quot;ASL-1.1&quot;).  
A copy of the ASL 1.1 is available at <a href="http://www.apache.org/licenses/LICENSE-1.1">http://www.apache.org/licenses/LICENSE-1.1</a>.
(a local copy can be found <a href="about_files/LICENSE-1.1.txt">here</a>)
</p>



<h4>Apache Velocity 1.5</h4>                                                                                                                                         
<p>The plug-in includes Apache Velocity 1.5 developed by the Apache Software Foundation.                                                                        
Your use of Apache Velocity is subject to the terms and conditions of the                                                                                         
Apache Software License 2.0. A copy of the license                                                                                                                   
 is contained in the file <a href="about_files/LICENSE.txt">LICENSE-2.0.txt</a>                                                                                          
and is also available at <a href="http://www.apache.org/licenses/LICENSE-2.0.txt">                                                                               
http://www.apache.org/licenses/LICENSE-2.0.txt</a>.</p>                                                                                                              



<h4>Plexus Velocity 1.1.3</h4>
<p>
Your use of Plexus Velocity 1.1.3 in binary code form contained in the plug-in is subject to the terms and conditions of the 
The Apache Software License, Version 2.0 (&quot;ASL&quot;).  
A copy of the ASL is available at <a href="http://www.apache.org/licenses/LICENSE-2.0">http://www.apache.org/licenses/LICENSE-2.0</a>.
(a local copy can be found <a href="about_files/LICENSE-2.0.txt">here</a>)
</p>
<p>The original binaries are available at the <a href="http://maven.org">Maven Central Repository</a>.</p>



</body>
</html>