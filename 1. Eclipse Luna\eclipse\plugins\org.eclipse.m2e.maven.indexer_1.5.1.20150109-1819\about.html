<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<title>About</title>
</head>
<body lang="EN-US">
<h2>About This Content</h2>
 
<p>June, 6 2011</p>	
<h3>License</h3>

<p>The Eclipse Foundation makes available all content in this plug-in (&quot;Content&quot;).  Unless otherwise 
indicated below, the Content is provided to you under the terms and conditions of the
Eclipse Public License Version 1.0 (&quot;EPL&quot;).  A copy of the EPL is available 
at <a href="http://www.eclipse.org/legal/epl-v10.html">http://www.eclipse.org/legal/epl-v10.html</a>.
For purposes of the EPL, &quot;Program&quot; will mean the Content.</p>

<p>If you did not receive this Content directly from the Eclipse Foundation, the Content is 
being redistributed by another party (&quot;Redistributor&quot;) and different terms and conditions may
apply to your use of any object code in the Content.  Check the Redistributor's license that was 
provided with the Content.  If no such license exists, contact the Redistributor.  Unless otherwise
indicated below, the terms and conditions of the EPL still apply to any source code in the Content
and such source code may be obtained at <a href="http://www.eclipse.org">http://www.eclipse.org</a>.</p>

<h3>Third Party Content</h3>

<p>
The Content includes items that have been sourced from third parties as set out below. If you 
did not receive this Content directly from the Eclipse Foundation, the following is provided 
for informational purposes only, and you should look to the Redistributor&rsquo;s license for 
terms and conditions of use.
</p>

<h4>Maven Indexer 3.1.0</h4>
<p>
The plug-in includes software developed by The Apache Software Foundation as part of the Maven project.
Your use of Maven Indexer 3.1.0 in binary code form contained in the plug-in is subject to the terms and conditions of the 
The Apache Software License, Version 2.0 (&quot;ASL&quot;).  
A copy of the ASL is available at <a href="http://maven.apache.org/license.html">http://maven.apache.org/license.html</a>.
(a local copy can be found <a href="about_files/LICENSE-2.0.txt">here</a>)
</p>
<p>The original binaries and source are available at the <a href="http://maven.org">Maven Central Repository</a>.</p>



<h4>Lucene 2.4.1</h4>
<p>
The plug-in includes software developed by The Apache Software Foundation as part of the Lucene project.
Your use of Lucene 2.4.1 in binary code form contained in the plug-in is subject to the terms and conditions of the 
The Apache Software License, Version 2.0 (&quot;ASL&quot;).  
A copy of the ASL is available at <a href="http://www.apache.org/licenses/LICENSE-2.0">http://www.apache.org/licenses/LICENSE-2.0</a>.
(a local copy can be found <a href="about_files/LICENSE-2.0.txt">here</a>)
</p>
<p>The original binaries and source are available at the <a href="http://maven.org">Maven Central Repository</a>.</p>


</body>
</html>