###############################################################################
# Copyright (c) 2006, 2007 IBM Corporation and others.
# All rights reserved. This program and the accompanying materials
# are made available under the terms of the Eclipse Public License v1.0
# which accompanies this distribution, and is available at
# http://www.eclipse.org/legal/epl-v10.html
#
# Contributors:
#     IBM Corporation - initial API and implementation
###############################################################################

#The directory used as a base for the all process
baseDirectory = <folder>

#Setting this to true will cause the dependency analysis to only be done on the features and plugins reachble from the ${featureList}.
filteredDependencyCheck=false

# A comma separated list of feature ids that will be part of the archive.
featureList = <featuresToGather>

# The list of {os, ws, arch} configurations to build.  This 
# value is a '&' separated list of ',' separate triples. 
# By default the value is *,*,* which is platform independant
#config=win32, win32, x86 & \
#	linux, gtk, ppc &\
# linux, gtk, x86 & \
#	linux, gtk, x86_64 & \
#	linux, motif, x86 & \
#	solaris, motif, sparc & \
#	solaris, gtk, sparc & \
#	aix, motif, ppc & \
#	hpux, motif, PA_RISC & \
#	macosx, carbon, ppc

#Set this variable to normalize plug-in and feature names while packaging 
normalizeWhilePackaging=true

################ DOWNLOAD ############################
#Skip any download and unzipping work because the things to be packaged are already available in ${target}
#Comment the property if you want the download and unzip to occur.
prefilledTarget = true


#Don't download packager map files, they should already exist in ${downloadDirectory}
#comment out this property to download the map file from packagerMapURL
#skipMaps = true
#The URL from which to download the packager map file. If more than one file needs to be downloaded, edit the getMapFiles target in your customTargets.xml
#packagerMapURL = 

#Don't download the archives.
#skipFetch = true

# A comma seperated list of the component from which the features listed in featureList can be found
# This is used as a optimization to avoid unnecessary downloads.
# * should be specified if you don't know
componentFilter=*

# A comma separated list of filters used to optimize the download of archives.
#Values in this filter should match the 
contentFilter=sdk


################ FOLDERS ############################
# The place in which all the scripts will be contained
workingDirectory = ${baseDirectory}/workingPlace
buildDirectory = ${workingDirectory}

# The folder in which all the zips will be downloaded
downloadDirectory = ${baseDirectory}/toPackage

# The folder where all the operations are being done
tempDirectory = ${baseDirectory}/temp

# The place in which the zips are located
sourceFolder = ${downloadDirectory}

################# SOURCES ###########################
# A comma separated lists of places where plugins and features will be looked for 
target = ${tempDirectory}/eclipse

################## MISC ##########################
# This controls the build id in the default name of the archive
buildId=MyProduct

# Type of build.  Used in naming the build output.  Typically this value is
# one of I, N, M, S, ...
buildType=I

#Set the name of the archive that will result from the product build.
#archiveNamePrefix = 

# Label for the build.  Used in naming the build output
buildLabel=${buildType}.${buildId}

#The format of the archive. By default a zip is created using antZip.
#archivesFormat=win32, win32, x86 - antZip& \
#	linux, gtk, ppc - antZip &\
#    linux, gtk, x86 - antZip& \
#	linux, gtk, x86_64 - antZip& \
# linux, motif, x86 - antZip& \
#	solaris, motif, sparc - antZip& \
#	solaris, gtk, sparc - antZip& \
#	aix, motif, ppc - antZip& \
#	hpux, motif, PA_RISC - antZip& \
#	macosx, carbon, ppc - antZip

# extra arguments to be passed to zip / unzip (-y is usually used on unix for zip)
zipargs=
unzipArgs=

# the prefix in the archive
archivePrefix=eclipse

# the folder in which the files that will be added to the archive will be placed
collectingFolder=eclipse


############### PACKAGING.PROPERTIES #############################
# a relative path to the file containing the properties for element to package
packagingProperties = packaging.properties
