<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.0"?>
<!--
     Copyright (c) 2005, 2012 IBM Corporation and others.
     All rights reserved. This program and the accompanying materials
     are made available under the terms of the Eclipse Public License v1.0
     which accompanies this distribution, and is available at
     http://www.eclipse.org/legal/epl-v10.html
    
     Contributors:
         IBM Corporation - initial API and implementation
 -->

<plugin>

    
<!-- Not to be extended com.sun.tools.jdi.VirtualMachineManagerImpl or org.eclipse.jdi.internal.VirtualMachineManagerImpl -->
   <extension-point id="jdiclient" name="%virtualMachineManagerImpl"  schema="schema/jdiclient.exsd"/>
   <extension-point id="javaLogicalStructures" name="%javaLogicalStructures" schema="schema/javaLogicalStructures.exsd"/>
   <extension-point id="breakpointListeners" name="%breakpointListeners.name" schema="schema/breakpointListeners.exsd"/>

<!-- Extensions -->
   <extension
         point="org.eclipse.debug.core.breakpoints">
      <breakpoint
            markerType="org.eclipse.jdt.debug.javaLineBreakpointMarker"
            class="org.eclipse.jdt.internal.debug.core.breakpoints.JavaLineBreakpoint"
            id="javaLineBreakpoint"
            name="%javaLineBreakpoint.name">
      </breakpoint>
      <breakpoint
            markerType="org.eclipse.jdt.debug.javaClassPrepareBreakpointMarker"
            class="org.eclipse.jdt.internal.debug.core.breakpoints.JavaClassPrepareBreakpoint"
            id="javaClassPrepareBreakpoint"
            name="%javaClassLoadBreakpoint.name">
      </breakpoint>      
      <breakpoint
            markerType="org.eclipse.jdt.debug.javaPatternBreakpointMarker"
            class="org.eclipse.jdt.internal.debug.core.breakpoints.JavaPatternBreakpoint"
            id="javaPatternBreakpoint"
            name="%javaLineBreakpoint.name">
      </breakpoint>
      <breakpoint
            markerType="org.eclipse.jdt.debug.javaTargetPatternBreakpointMarker"
            class="org.eclipse.jdt.internal.debug.core.breakpoints.JavaTargetPatternBreakpoint"
            id="javaTargetPatternBreakpoint"
            name="%javaLineBreakpoint.name">
      </breakpoint>
      <breakpoint
            markerType="org.eclipse.jdt.debug.javaExceptionBreakpointMarker"
            class="org.eclipse.jdt.internal.debug.core.breakpoints.JavaExceptionBreakpoint"
            id="javaExceptionBreakpoint"
            name="%javaExceptionBreakpoint.name">
      </breakpoint>
      <breakpoint
            markerType="org.eclipse.jdt.debug.javaWatchpointMarker"
            class="org.eclipse.jdt.internal.debug.core.breakpoints.JavaWatchpoint"
            id="javaWatchpoint"
            name="%javaWatchpoint.name">
      </breakpoint>
      <breakpoint
            markerType="org.eclipse.jdt.debug.javaMethodBreakpointMarker"
            class="org.eclipse.jdt.internal.debug.core.breakpoints.JavaMethodBreakpoint"
            id="javaMethodBreakpoint"
            name="%javaMethodBreakpoint.name">
      </breakpoint>
      <breakpoint
            markerType="org.eclipse.jdt.debug.javaMethodEntryBreakpointMarker"
            class="org.eclipse.jdt.internal.debug.core.breakpoints.JavaMethodEntryBreakpoint"
            id="javaMethodEntryBreakpoint"
            name="%javaMethodBreakpoint.name">
      </breakpoint>
      <breakpoint
            markerType="org.eclipse.jdt.debug.javaStratumLineBreakpointMarker"
            class="org.eclipse.jdt.internal.debug.core.breakpoints.JavaStratumLineBreakpoint"
            id="javaStratumLineBreakpointMarker"
            name="%javaLineBreakpoint.name">
      </breakpoint>
   </extension>
   <extension
         id="javaBreakpointMarker"
         name="%JavaBreakpoint.name"
         point="org.eclipse.core.resources.markers">
      <super
            type="org.eclipse.debug.core.breakpointMarker">
      </super>
   </extension>
   <extension
         id="javaClassPrepareBreakpointMarker"
         name="%JavaClassLoadBreakpoint.name"
         point="org.eclipse.core.resources.markers">
      <super
            type="org.eclipse.jdt.debug.javaBreakpointMarker">
      </super>
      <persistent
            value="true">
      </persistent>
   </extension>   
   <extension
         id="commonJavaLineBreakpointMarker"
         name="%CommonJavaLineBreakpoint.name"
         point="org.eclipse.core.resources.markers">
      <super
            type="org.eclipse.jdt.debug.javaBreakpointMarker">
      </super>
      <super
            type="org.eclipse.debug.core.lineBreakpointMarker">
      </super>
      <persistent
            value="true">
      </persistent>
      <attribute
            name="org.eclipse.jdt.debug.core.typeName">
      </attribute>
      <attribute
            name="org.eclipse.jdt.debug.core.installCount">
      </attribute>
      <attribute
            name="org.eclipse.jdt.debug.core.hitCount">
      </attribute>
      <attribute
            name="org.eclipse.jdt.debug.core.expired">
      </attribute>
   </extension>
   <extension
         id="javaLineBreakpointMarker"
         name="%JavaLineBreakpoint.name"
         point="org.eclipse.core.resources.markers">
      <super
            type="org.eclipse.jdt.debug.commonJavaLineBreakpointMarker">
      </super>
      <persistent
            value="true">
      </persistent>
   </extension>
   <extension
         id="javaPatternBreakpointMarker"
         name="%JavaPatternBreakpoint.name"
         point="org.eclipse.core.resources.markers">
      <super
            type="org.eclipse.jdt.debug.commonJavaLineBreakpointMarker">
      </super>
      <persistent
            value="true">
      </persistent>
      <attribute
            name="org.eclipse.jdt.debug.core.pattern">
      </attribute>
      <attribute
            name="org.eclipse.jdt.debug.core.sourceName">
      </attribute>
   </extension>
   <extension
         id="javaTargetPatternBreakpointMarker"
         name="%JavaTargetPatternBreakpoint.name"
         point="org.eclipse.core.resources.markers">
      <super
            type="org.eclipse.jdt.debug.commonJavaLineBreakpointMarker">
      </super>
      <persistent
            value="true">
      </persistent>
      <attribute
            name="org.eclipse.jdt.debug.core.sourceName">
      </attribute>
   </extension>
   <extension
         id="javaExceptionBreakpointMarker"
         name="%JavaExceptionBreakpoint.name"
         point="org.eclipse.core.resources.markers">
      <super
            type="org.eclipse.jdt.debug.javaBreakpointMarker">
      </super>
      <persistent
            value="true">
      </persistent>
      <attribute
            name="org.eclipse.jdt.debug.core.caught">
      </attribute>
      <attribute
            name="org.eclipse.jdt.debug.core.uncaught">
      </attribute>
      <attribute
            name="org.eclipse.jdt.debug.core.checked">
      </attribute>
   </extension>
   <extension
         id="javaWatchpointMarker"
         name="%JavaWatchpoint.name"
         point="org.eclipse.core.resources.markers">
      <super
            type="org.eclipse.jdt.debug.javaLineBreakpointMarker">
      </super>
      <persistent
            value="true">
      </persistent>
      <attribute
            name="org.eclipse.jdt.debug.core.fieldName">
      </attribute>
      <attribute
            name="org.eclipse.jdt.debug.core.access">
      </attribute>
      <attribute
            name="org.eclipse.jdt.debug.core.modification">
      </attribute>
      <attribute
            name="org.eclipse.jdt.debug.core.auto_disabled">
      </attribute>
   </extension>
   <extension
         id="javaMethodBreakpointMarker"
         name="%JavaMethodBreakpoint.name"
         point="org.eclipse.core.resources.markers">
      <super
            type="org.eclipse.jdt.debug.javaLineBreakpointMarker">
      </super>
      <persistent
            value="true">
      </persistent>
      <attribute
            name="org.eclipse.jdt.debug.core.methodName">
      </attribute>
      <attribute
            name="org.eclipse.jdt.debug.core.methodSignature">
      </attribute>
      <attribute
            name="org.eclipse.jdt.debug.core.entry">
      </attribute>
      <attribute
            name="org.eclipse.jdt.debug.core.exit">
      </attribute>
      <attribute
            name="org.eclipse.jdt.debug.core.native">
      </attribute>
   </extension>
   <extension
         id="javaMethodEntryBreakpointMarker"
         name="%JavaMethodEntryBreakpoint.name"
         point="org.eclipse.core.resources.markers">
      <super
            type="org.eclipse.jdt.debug.javaLineBreakpointMarker">
      </super>
      <persistent
            value="true">
      </persistent>
      <attribute
            name="org.eclipse.jdt.debug.core.methodName">
      </attribute>
      <attribute
            name="org.eclipse.jdt.debug.core.methodSignature">
      </attribute>
   </extension>
   <extension
         id="javaStratumLineBreakpointMarker"
         name="%JavaStratumLineBreakpoint.name"
         point="org.eclipse.core.resources.markers">
      <super
            type="org.eclipse.jdt.debug.commonJavaLineBreakpointMarker">
      </super>
      <persistent
            value="true">
      </persistent>
      <attribute
            name="org.eclipse.jdt.debug.core.stratum">
      </attribute>
      <attribute
            name="org.eclipse.jdt.debug.core.sourceName">
      </attribute>
      <attribute
            name="org.eclipse.jdt.debug.core.sourcePath">
      </attribute>
      <attribute
            name="org.eclipse.jdt.debug.core.pattern">
      </attribute>
   </extension>

   
   <extension point="org.eclipse.core.expressions.propertyTesters">
      <propertyTester
      		namespace="org.eclipse.jdt.debug"
            properties="isMultiStrata"
            type="org.eclipse.debug.core.model.IStackFrame"
            class="org.eclipse.jdt.internal.debug.core.JavaDebugPropertyTester"
            id="org.eclipse.jdt.debug.PropertyTester">
      </propertyTester>        
   </extension>
   
  	<extension point="org.eclipse.core.runtime.preferences">
		<initializer class="org.eclipse.jdt.internal.debug.core.JDIDebugPluginPreferenceInitializer"/>
	</extension>
   <extension
         point="org.eclipse.jdt.debug.javaLogicalStructures">
      <javaLogicalStructure
            subtypes="true"
            value="return entrySet().toArray();"
            type="java.util.Map"
            description="%descriptionMap"/>
      <javaLogicalStructure
            subtypes="true"
            value="return toArray();"
            type="java.util.Collection"
            description="%descriptionCollection"/>
      <javaLogicalStructure
            subtypes="true"
            type="java.util.Map$Entry"
            description="%descriptionMapEntry">
         <variable
               value="return getKey();"
               name="key"/>
         <variable
               value="return getValue();"
               name="value"/>
      </javaLogicalStructure>
		<javaLogicalStructure
		      description="%descriptionDomNode"
		      subtypes="true"
		      type="org.w3c.dom.Node">
		   <variable
		         name="children"
		         value="org.w3c.dom.NodeList children = getChildNodes(); org.w3c.dom.Node[] nodeArray = new org.w3c.dom.Node[children.getLength()];for (int i = 0; i &lt; children.getLength(); i++) { nodeArray[i] = children.item(i); } return nodeArray;">
		   </variable>
		   <variable
		         name="attributes"
		         value="org.w3c.dom.NamedNodeMap attributes = getAttributes();if (attributes == null) return null; org.w3c.dom.Node[] nodes = new org.w3c.dom.Node[attributes.getLength()];for (int i = 0; i &lt; attributes.getLength(); i++) {nodes[i] = attributes.item(i);}return nodes;">
		   </variable>
		</javaLogicalStructure>
      
   </extension>
   <extension
         point="org.eclipse.debug.core.logicalStructureProviders">
      <logicalStructureProvider
            class="org.eclipse.jdt.internal.debug.core.logicalstructures.JavaLogicalStructures"
            modelIdentifier="org.eclipse.jdt.debug"/>
   </extension>
   <extension
         point="org.eclipse.debug.core.breakpointImportParticipants">
      <importParticipant
            participant="org.eclipse.jdt.internal.debug.core.breakpoints.JavaBreakpointImportParticipant"
            type="org.eclipse.jdt.debug.javaClassPrepareBreakpointMarker">
      </importParticipant>
      <importParticipant
            participant="org.eclipse.jdt.internal.debug.core.breakpoints.JavaBreakpointImportParticipant"
            type="org.eclipse.jdt.debug.javaExceptionBreakpointMarker">
      </importParticipant>
      <importParticipant
            participant="org.eclipse.jdt.internal.debug.core.breakpoints.JavaBreakpointImportParticipant"
            type="org.eclipse.jdt.debug.javaLineBreakpointMarker">
      </importParticipant>
      <importParticipant
            participant="org.eclipse.jdt.internal.debug.core.breakpoints.JavaBreakpointImportParticipant"
            type="org.eclipse.jdt.debug.javaMethodBreakpointMarker">
      </importParticipant>
      <importParticipant
            participant="org.eclipse.jdt.internal.debug.core.breakpoints.JavaBreakpointImportParticipant"
            type="org.eclipse.jdt.debug.javaMethodEntryBreakpointMarker">
      </importParticipant>
      <importParticipant
            participant="org.eclipse.jdt.internal.debug.core.breakpoints.JavaBreakpointImportParticipant"
            type="org.eclipse.jdt.debug.javaWatchpointMarker">
      </importParticipant>
   </extension>
   
</plugin>
