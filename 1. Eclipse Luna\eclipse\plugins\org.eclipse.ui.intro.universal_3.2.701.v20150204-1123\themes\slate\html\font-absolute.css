/*******************************************************************************
 * Copyright (c) 2008, 2009 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials 
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * 
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *******************************************************************************/

/* The label part of the folding section */
.section-title-link .section-title {
	font-size : 10pt;
}

h2 {
	font-size : 13pt;
}

/* For regular div labels */
H4 .div-label {
	font-size: 10pt; 
}

/* For the main page content's title */
#content-header H4 .div-label {
	font-size: 23pt; 
}


/* Page description if the page has it. */
.page-description {
	font-size: 10pt; 
}

/* General link labels */
a .link-label {
	font-size : 10pt;
}

/* Floating link labels for navigation links */
#navigation-links a .link-label {
	font-size : 8pt;
}

/* Text in links. */
a .text {
	font-size : 8pt;
}

p .group-description {
	font-size : 10pt;
}

.content-link .link-label {
	font-size: 11pt; 
}

.content-link .text {
	font-size: 10pt;
}

.categoryContentnav {
	font-size:10pt; 
}

.contentpgNavhover {
	font-size: 8pt; 
}

.topicList {
	font-size:8pt; 
}

/* 
 * Root page settings
 */
#root .intro-header H1 {
	font-size : 23pt;
}

/* Link label properties */
#root #page-links a .link-label {
	font-size : 14pt;
}

/*
 * Standby page settings
 */

#standby .intro-header H1 {
	font-size : 15pt;
}

#standby #page-links a .link-label {
	font-size : 10pt;
}

#standby #page-links a p .text {
	font-size : 10pt;
}