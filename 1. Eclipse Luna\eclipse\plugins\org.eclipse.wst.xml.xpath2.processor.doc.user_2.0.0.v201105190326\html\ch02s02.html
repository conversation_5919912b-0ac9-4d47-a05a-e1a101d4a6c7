<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Schema Aware</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch02.html" title="How to feed Psychopath XPath expressions"><link rel="prev" href="ch02.html" title="How to feed Psychopath XPath expressions"><link rel="next" href="ch02s03.html" title="How to use the XPath 2.0 grammar with PsychoPath"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Schema Aware"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Schema_Aware"></a>Schema Aware</h2></div></div></div><div class="literallayout"><p>/**<br>
&nbsp;*&nbsp;First&nbsp;load&nbsp;and&nbsp;optionally&nbsp;validate&nbsp;the&nbsp;XML&nbsp;document&nbsp;<br>
&nbsp;*/<br>
//&nbsp;Create&nbsp;an&nbsp;InputStream&nbsp;from&nbsp;the&nbsp;XML&nbsp;document<br>
InputStream&nbsp;is&nbsp;=&nbsp;new&nbsp;FileInputStream("XPexample.xml");<br>
</p></div><div class="literallayout"><p>SchemaFactory&nbsp;schemaFactory&nbsp;=&nbsp;new&nbsp;XMLSchemaFactory();<br>
URL&nbsp;schemaURL&nbsp;=&nbsp;new&nbsp;File("XPexample.xsd").toURL();<br>
Schema&nbsp;jaxpschema&nbsp;=&nbsp;schemaFactory.newSchema(schemaURL);<br>
</p></div><div class="literallayout"><p>//&nbsp;Initializing&nbsp;the&nbsp;Xerces&nbsp;DOM&nbsp;loader.<br>
DOMLoader&nbsp;loader&nbsp;=&nbsp;new&nbsp;XercesLoader(jaxpschema);<br>
loader.set_validating(true);<br>
</p></div><div class="literallayout"><p>//&nbsp;Optionally&nbsp;set&nbsp;flag&nbsp;to&nbsp;validate&nbsp;XML&nbsp;document<br>
//&nbsp;loader.set_validating(validate);<br>
//&nbsp;Loads&nbsp;the&nbsp;XML&nbsp;document&nbsp;and&nbsp;stores&nbsp;the&nbsp;DOM&nbsp;root<br>
Document&nbsp;doc&nbsp;=&nbsp;loader.load(is);<br>
</p></div><div class="literallayout"><p>//&nbsp;Initialising&nbsp;the&nbsp;StaticContext&nbsp;using&nbsp;a&nbsp;builder&nbsp;with&nbsp;suitable&nbsp;defaults.<br>
StaticContextBuilder&nbsp;scb&nbsp;=&nbsp;new&nbsp;StaticContextBuilder();<br>
scb.withNamespace("xs",&nbsp;"http://www.w3.org/2001/XMLSchema");<br>
scb.withTypeModel(new&nbsp;XercesTypeModel(doc));<br>
</p></div><div class="literallayout"><p>/**<br>
&nbsp;*&nbsp;Parsing&nbsp;the&nbsp;XPath&nbsp;2.0&nbsp;expression&nbsp;into&nbsp;an&nbsp;executable&nbsp;expression,&nbsp;including<br>
&nbsp;*&nbsp;a&nbsp;static&nbsp;check&nbsp;and&nbsp;verification.<br>
&nbsp;*/<br>
String&nbsp;xpathText&nbsp;=&nbsp;"for&nbsp;$elem&nbsp;in&nbsp;//*[.&nbsp;instance&nbsp;of&nbsp;xs:normalizedString]&nbsp;return&nbsp;concat(local-name($elem),&nbsp;'&nbsp;:&nbsp;',&nbsp;$elem/text())";<br>
XPath2Expression&nbsp;expr&nbsp;=&nbsp;new&nbsp;Engine().parseExpression(xpathText,&nbsp;scb);<br>
</p></div><div class="literallayout"><p>//&nbsp;Evaluates&nbsp;the&nbsp;XPath&nbsp;2.0&nbsp;expression,&nbsp;storing&nbsp;the&nbsp;result<br>
//&nbsp;in&nbsp;the&nbsp;ResultSequence<br>
ResultSequence&nbsp;rs&nbsp;=&nbsp;expr.evaluate(new&nbsp;DynamicContextBuilder(scb),<br>
	new&nbsp;Object[]&nbsp;{&nbsp;doc&nbsp;});<br>
</p></div><div class="literallayout"><p>for&nbsp;(int&nbsp;i&nbsp;=&nbsp;0;&nbsp;i&nbsp;&lt;&nbsp;rs.size();&nbsp;++i)&nbsp;{<br>
	System.out.println("#"&nbsp;+&nbsp;i&nbsp;+&nbsp;":&nbsp;"&nbsp;+&nbsp;rs.value(i));<br>
}<br>
</p></div><p>XPath 2.0 defines everything to be a sequence of items, including the arguments to expressions and the result of operations. Thus, the overall result of an XPath expression evaluation is also a sequence of items. PsychoPath uses the class ResultSequence as a Collections wrapper to store these sequences and therefore, the result of an evaluation is of this type also. The ResultSequence consists of zero or more items; an item may be a node or a simple-value. &ldquo;Hello World!&rdquo; is an example of a single value with length 1. A general sequence could be written as (&ldquo;a&rdquo;, &ldquo;s&rdquo;, &ldquo;d&rdquo;, &ldquo;f&rdquo;). </p><p>Extraction of certain items from the ResultSequence class is described below, with details of the different operations that one might apply on the ResultSequence. Consider that &rsquo;rs&rsquo; is the ResultSequence, then: </p><p>// Will return the number of elements in the sequence, in this </p><div class="literallayout"><p>//&nbsp;case&nbsp;of&nbsp;&rsquo;Hello&nbsp;World!&rsquo;&nbsp;expression&nbsp;size&nbsp;=&nbsp;1.&nbsp;<br>
</p></div><div class="literallayout"><p>rs.size();<br>
</p></div><div class="literallayout"><p>//&nbsp;Will&nbsp;return&nbsp;the&nbsp;n&rsquo;th&nbsp;element&nbsp;in&nbsp;the&nbsp;sequence,&nbsp;in&nbsp;this&nbsp;case&nbsp;of&nbsp;<br>
//&nbsp;&rsquo;Hello&nbsp;World!&rsquo;,&nbsp;if&nbsp;n&nbsp;=&nbsp;0,&nbsp;then&nbsp;it&nbsp;will&nbsp;return<br>
//&nbsp;&ldquo;Hello&nbsp;World!&rdquo;.<br>
</p></div><div class="literallayout"><p>rs.value(n);<br>
</p></div><div class="literallayout"><p>//Will&nbsp;return&nbsp;true&nbsp;if&nbsp;the&nbsp;sequence&nbsp;is&nbsp;empty.<br>
rs.empty();&nbsp;<br>
</p></div><div class="literallayout"><p>//&nbsp;Will&nbsp;return&nbsp;the&nbsp;first&nbsp;element&nbsp;of&nbsp;the&nbsp;sequence,&nbsp;<br>
//&nbsp;in&nbsp;this&nbsp;example&nbsp;it&nbsp;will&nbsp;return&nbsp;xs:string&nbsp;of&nbsp;&ldquo;Hello&nbsp;World!&rdquo;&nbsp;<br>
rs.firstValue()&nbsp;<br>
</p></div><p> 

				 However, all the items extracted will be of the type&rsquo;s "native" value (statically known as Object) and need to be casted into its actual subtype. </p><p>Certain operations always return a particular type and using this knowledge, the extracted item can be immediately casted. In our example &ldquo;Hello World!&rdquo; returns a string (easily known as it is inside the quotes &rsquo; &rsquo; ), so this can safely be casted as such: 
				</p><div class="literallayout"><p>String&nbsp;string&nbsp;=&nbsp;(String)(rs.value(0));<br>
&nbsp;<br>
</p></div><p>The details of how to cast extracted items from Object into their actual subtypes with examples is in the next section on How to use each production in the grammar. As an alternative, you can call
				</p><div class="literallayout"><p>ItemType&nbsp;itype&nbsp;=&nbsp;rs.itemType(n);<br>
Which&nbsp;will&nbsp;return&nbsp;the&nbsp;type&nbsp;of&nbsp;the&nbsp;n'th&nbsp;item&nbsp;in&nbsp;the&nbsp;result&nbsp;sequence.<br>
</p></div><p>
</p></div></body></html>