<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Operators that Generate Sequences</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch04.html" title="How to use XPath 2.0 operators with PsychoPath"><link rel="prev" href="ch04s08.html" title="Union, Intersection and Except"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Operators that Generate Sequences"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Operators_that_Generate_Sequences"></a>Operators that Generate Sequences</h2></div></div></div><div class="literallayout"><p>(1&nbsp;to&nbsp;3)<br>
</p></div><p>from within a Java application, in order to extract the result from the result sequence, one would have to use this code: </p><p>int n = (XSInteger)rs.first()).stringvalue(); println(n);</p><p>which returns the sequence consisting of 1, 2, 3. </p><div class="itemizedlist"><ul class="itemizedlist" type="disc"><li class="listitem"><p></p><div class="itemizedlist"><ul class="itemizedlist" type="circle"><li class="listitem"><p></p><div class="itemizedlist"><ul class="itemizedlist" type="square"><li class="listitem"><p>-</p></li></ul></div></li></ul></div></li></ul></div><p>
				<a class="ulink" href="/wiki/Category:Draft_Documentation" target="_top">Category:Draft_Documentation</a>
			</p></div></body></html>