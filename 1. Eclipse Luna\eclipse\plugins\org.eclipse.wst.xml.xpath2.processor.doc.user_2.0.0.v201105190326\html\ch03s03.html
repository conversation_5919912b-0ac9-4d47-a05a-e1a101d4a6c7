<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Functions on Numeric Values</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch03.html" title="How to use XPath 2.0 functions with PsychoPath"><link rel="prev" href="ch03s02.html" title="Constructor Functions"><link rel="next" href="ch03s04.html" title="Functions to Assemble and Disassemble Strings"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Functions on Numeric Values"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Functions_on_Numeric_Values"></a>Functions on Numeric Values</h2></div></div></div><div class="literallayout"><p>ceiling(xs:float(&lsquo;10.4&rsquo;))<br>
</p></div><p>from within a Java application, in order to extract the result from the result sequence, one would have to use this code: </p><p>float n = ((XSFloat)rs.first()).floatvalue(); println(n);</p><p>in order to get the result of &lsquo;11.0&rsquo; </p></div></body></html>