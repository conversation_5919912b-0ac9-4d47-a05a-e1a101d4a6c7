/*******************************************************************************
 * Copyright (c) 2006 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials 
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * 
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *******************************************************************************/

/* show the "selected" image for this page */
#navigation-links a#overview img, 
#navigation-links a#overview:hover img,
#navigation-links a#overview:focus img,
#navigation-links a#overview:active img {
	background-image : url(../graphics/icons/ctool/ov_nav_64.gif);
	top : 2px; 
	left : -3px;
}

#navigation-links {
	background-image: url(../graphics/contentpage/ov_banner.jpg);
}

#navigation-links a:hover#overview .link-label,
#navigation-links a:focus#overview .link-label,
#navigation-links a:active#overview .link-label {
	display : none;
}

#navigation-links a:hover#overview,
#navigation-links a:focus#overview,
#navigation-links a:active#overview {
	background-image : none;
}

#navigation-links a:hover#overview .link-extra-div,
#navigation-links a:focus#overview .link-extra-div,
#navigation-links a:active#overview .link-extra-div {
	display: none;
}