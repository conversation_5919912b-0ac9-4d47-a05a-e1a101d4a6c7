/*******************************************************************************
 * Copyright (c) 2010, 2014 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *     <PERSON> <<PERSON>.<EMAIL>> - Bug 420836
 *******************************************************************************/

.MPart.busy {
	font-style: italic;
}

.MPart.highlighted {
	font-weight: bold;
}

.MPartStack, .MPart {
	font-family: '#org-eclipse-ui-workbench-TAB_TEXT_FONT';
}

CTabItem:selected {
	color: '#org-eclipse-ui-workbench-ACTIVE_TAB_TEXT_COLOR';
}

.MTrimmedWindow {
	margin-top: 0px;
	margin-bottom: 0px;
	margin-left: 2px;
	margin-right: 2px;
}

.MPartStack {
	swt-tab-renderer: null;
	swt-selected-tabs-background: '#org-eclipse-ui-workbench-INACTIVE_TAB_BG_START' '#org-eclipse-ui-workbench-INACTIVE_TAB_BG_END' 100%;
	swt-simple: false;
	swt-mru-visible: true;
	color: '#org-eclipse-ui-workbench-INACTIVE_TAB_TEXT_COLOR';
}

.MPartStack.active {
	swt-selected-tabs-background: '#org-eclipse-ui-workbench-ACTIVE_TAB_BG_START' '#org-eclipse-ui-workbench-ACTIVE_TAB_BG_END' 100%;
}

.MPartStack.active.noFocus {
	swt-selected-tabs-background: '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_BG_START' '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_BG_END' 100%;
}

.MPartStack.active.noFocus > CTabItem:selected {
	color: '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_TEXT_COLOR';
}

#PerspectiveSwitcher  {
	eclipse-perspective-keyline-color: #ECE9D8 #FFFFFF;
}

.MToolControl.TrimStack {
	frame-image:  url(./winClassicTSFrame.png);
	handle-image:  url(./winClassicHandle.png);
	frame-cuts: 5px 1px 5px 16px;
}

.MToolBar.Draggable {
	handle-image:  url(./dragHandle.png);
}

.MToolControl.Draggable {
	handle-image:  url(./dragHandle.png);
}

.DragFeedback {
	background-color: COLOR-WIDGET-NORMAL-SHADOW;
}

.ModifiedDragFeedback {
	background-color: #A0A000;
}

#org-eclipse-ui-editorss {
	swt-tab-height: 8px;
}
