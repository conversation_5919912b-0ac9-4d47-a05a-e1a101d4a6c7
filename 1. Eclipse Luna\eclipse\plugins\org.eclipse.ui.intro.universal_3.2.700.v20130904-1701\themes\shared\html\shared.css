/*******************************************************************************
 * Copyright (c) 2006, 2007 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials 
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * 
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *******************************************************************************/

/* 
 * This file contains styles that are shared between multiple themes.
 */

/* Folding section settings. */

/* The foldable part of the section. It is off by default. */
.section-body {
	display: none;
}

/* The label part of the folding section */
.section-title-link .section-title {
	display : inline;
}

.section-title-link .section-title,
.section-title-link:focus .section-title {
	color: #00517d;
}

.section-title-link {
	vertical-align: bottom;
}

/* The 'open' toggle image part of the folding section. */
#page-content .section-title-link .section-toggle-image-open {
	display: none;
	clear: right;
	width : 7px;
	height : 7px;
	background-image : url(../graphics/icons/ctool/widget_open.gif);
}

/* The 'closed' toggle image part of the folding section. */
#page-content .section-title-link .section-toggle-image-closed {
	display: inline;
	clear: right;
	width : 7px;
	height : 7px;
	background-image : url(../graphics/icons/ctool/widget_closed.gif);
}

/*
 * Section title during hover.
 */
.section-title-link:hover .section-title,
.section-title-link:active .section-title {
	color: #6699cc;
}

.section-title-link:hover .section-title {
	text-decoration: underline;
}

/*
 * Toggle image during hover.
 */
#page-content .section-title-link:hover .section-toggle-image-closed,
#page-content .section-title-link:active .section-toggle-image-closed {
	background-image : url(../graphics/icons/ctool/widget_closed_hov.gif);
}
#page-content .section-title-link:hover .section-toggle-image-open,
#page-content .section-title-link:active .section-toggle-image-open {
	background-image : url(../graphics/icons/ctool/widget_open_hov.gif);
}

/* 
 * Importance highlights for page content. Gradient image is
 * tiled vertically. In addition, background color is applied
 * to fill in the areas not covered by the gradient image.
 */
.importance-high,
.importance-low,
.importance-callout,
.importance-medium,
.importance-new {
	background-position: top left;
	background-repeat: repeat-y;
}

body .importance-low {
	background-color: #ffffff;
}

body .importance-new {
	background-image: url("../graphics/contentpage/ov_high.gif");	
	background-color: #fffacd;
}

body #overview .importance-high {
	background-color: #fff7da;
	background-image: url("../graphics/contentpage/ov_high.gif");
}

body #overview .importance-medium {
	background-color: #fffbec;
	background-image: url("../graphics/contentpage/ov_med.gif");
}

body #tutorials .importance-high,
body #samples .importance-high {
	background-color: #e1eaf2;
	background-image: url("../graphics/contentpage/tu-sa_high.gif");
}

body #tutorials .importance-medium,
body #samples .importance-medium {
	background-color: #f0f4f8;
	background-image: url("../graphics/contentpage/tu-sa_med.gif");
}

body #whatsnew .importance-high,
body #firststeps .importance-high {
	background-color: #f3ecdb;
	background-image: url("../graphics/contentpage/wn-fs_high.gif");
}

body #whatsnew .importance-medium,
body #firststeps .importance-medium {
	background-color: #f7f2e7;
	background-image: url("../graphics/contentpage/wn-fs_med.gif");
}

body #webresources .importance-high,
body #migrate .importance-high {
	background-color: #ecf4d7;
	background-image: url("../graphics/contentpage/wr-mi_high.gif");
}

body #webresources .importance-medium,
body #migrate .importance-medium {
	background-color: #f5f9eb;
	background-image: url("../graphics/contentpage/wr-mi_med.gif");
}

.importance-callout {
	background-color: #eeeeee;
	background-image: url("../graphics/contentpage/grey_callout.gif");
}

ul.news-list {
	list-style-image: url("../graphics/icons/ctool/arrow.gif");
}
 