# plugin_customization.ini 
# sets default values for plug-in-specific preferences
# keys are qualified by plug-in id
# e.g., com.example.acmeplugin/myproperty=myvalue
# java.io.Properties file (ISO 8859-1 with "\" escapes)
# "%key" are externalized strings defined in plugin_customization.properties
# This file does not need to be translated.

# Property "org.eclipse.ui/defaultPerspectiveId" controls the 
# perspective that the workbench opens initially
org.eclipse.ui/defaultPerspectiveId=org.eclipse.jst.j2ee.J2EEPerspective

# new-style tabs by default
org.eclipse.ui/SHOW_TRADITIONAL_STYLE_TABS=false

# put the perspective switcher on the top right
org.eclipse.ui/DOCK_PERSPECTIVE_BAR=topRight

# show progress on startup
org.eclipse.ui/SHOW_PROGRESS_ON_STARTUP=true

# show build id in the splash - only for nightly, integration, and milestone builds
org.eclipse.ui.workbench/SHOW_BUILDID_ON_STARTUP=false

# use the window set by default
org.eclipse.ui/USE_WINDOW_WORKING_SET_BY_DEFAULT=true

# lightweight auto-refresh on access by default
org.eclipse.core.resources/refresh.lightweight.enabled=true

# enable line number ruler in all textual editors by default
org.eclipse.ui.editors/lineNumberRuler=true

# Intro-related preferences (since 3.2)

# Welcome theme to use
org.eclipse.ui.intro/INTRO_THEME = org.eclipse.ui.intro.universal.slate

# Root page links to show in the Universal Welcome
org.eclipse.ui.intro.universal/INTRO_ROOT_PAGES = overview,tutorials,samples,whatsnew

# Initial page layout of the Universal Welcome
org.eclipse.ui.intro.universal/INTRO_DATA = product:introData.xml

# Order help books in table of contents
org.eclipse.help/HELP_DATA = helpData.xml

#Ensure m2e indexing is off by default
org.eclipse.m2e.core/eclipse.m2.updateIndexes=false
