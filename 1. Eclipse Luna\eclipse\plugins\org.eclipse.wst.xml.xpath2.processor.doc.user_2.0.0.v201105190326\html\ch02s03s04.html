<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Set difference, intersection and Union</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch02s03.html" title="How to use the XPath 2.0 grammar with PsychoPath"><link rel="prev" href="ch02s03s03.html" title="Axis steps"><link rel="next" href="ch02s03s05.html" title="Arithmetic Expressions"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Set difference, intersection and Union"><div class="titlepage"><div><div><h3 class="title"><a name="Set_difference.2C_intersection_and_Union"></a>Set difference, intersection and Union</h3></div></div></div><p>The expression E1 except E2 selects all nodes that are in E1 unless they are also in E2. Both expressions must return sequences of nodes. The results are returned in document order. For example, @* except @note returns all attributes except the note attribute. The expression E1 intersect E2 selects all nodes that are in both E1 and E2. Both expressions must return sequences of nodes. The results are returned in document order. The expression E1 union E2 selects all nodes that are in either E1 or E2 or both. Both expressions must return sequences of nodes. The results are returned in document order. A complete example of the above expression would be as follows. Consider an XML document which looks like this: </p><div class="literallayout"><p>&lt;source&nbsp;lang="xml"&gt;<br>
&lt;nodes&gt;<br>
&nbsp;&nbsp;&lt;a&gt;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&lt;connecteda&gt;A&lt;/connecteda&gt;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&lt;connecteda&gt;B&lt;/connecteda&gt;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&lt;connecteda&gt;C&lt;/connecteda&gt;<br>
&nbsp;&nbsp;&lt;/a&gt;<br>
&nbsp;&nbsp;&lt;b&gt;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&lt;connectedb&gt;B&lt;/connectedb&gt;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&lt;connectedb&gt;C&lt;/connectedb&gt;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&lt;connectedb&gt;D&lt;/connectedb&gt;<br>
&nbsp;&nbsp;&lt;/b&gt;<br>
&lt;/nodes&gt;<br>
</p></div><p> 
					then an example of each of the expressions would be: </p><div class="literallayout"><p>data(/a/*)&nbsp;union&nbsp;data(/b/*)<br>
</p></div><p> 

					<span class="italic">'result: '</span> 
				</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>xs:string: A </p></li><li class="listitem"><p>xs:string: B </p></li><li class="listitem"><p>xs:string: C </p></li><li class="listitem"><p>xs:string: D
</p></li></ol></div><div class="literallayout"><p>data(/a/*)&nbsp;intersect&nbsp;data(/b/*)<br>
</p></div><p> 

					<span class="bold"><strong>result:</strong></span> 
				</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>xs:string: B </p></li><li class="listitem"><p>xs:string: C
</p></li></ol></div><div class="literallayout"><p>data(/a/*)&nbsp;except&nbsp;data(/b/*)<br>
</p></div><p> 

					<span class="bold"><strong>result:</strong></span> 
				</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>xs:string: D
</p></li></ol></div></div></body></html>