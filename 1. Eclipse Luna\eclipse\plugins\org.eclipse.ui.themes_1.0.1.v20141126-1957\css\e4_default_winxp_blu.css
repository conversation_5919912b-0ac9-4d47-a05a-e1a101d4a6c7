/*******************************************************************************
 * Copyright (c) 2010, 2014 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *     <PERSON> <<PERSON>.<EMAIL>> - Bug 420836
 *******************************************************************************/

@import url("platform:/plugin/org.eclipse.ui.themes/css/e4_basestyle.css");

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_START {
	color: #F1F4FD;
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_END {
	color: #C9D7F2;
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_TAB_OUTER_KEYLINE_COLOR {
	color: #B8C7E5;
}

.MTrimmedWindow {
	background-color: #F0ECE0;
}

.MPartStack {
	swt-simple: true;
	swt-mru-visible: false;
}

.MTrimBar {
	background-color: #F0ECE0;
}


.MTrimBar#org-eclipse-ui-main-toolbar  {
	background-image:  url(./winXPBlue.png);
}

.MToolControl.TrimStack {
	frame-image:  url(./winXPBluTSFrame.png);
	handle-image:  url(./winXPBluHandle.png);
}

#PerspectiveSwitcher  {
	background-color: #F5F3ED #F0ECE0 100%;
	eclipse-perspective-keyline-color: #7F91B5 #7F91B5;
}

#org-eclipse-ui-editorss {
	swt-tab-height: 8px;
	padding: 0px 5px 7px;
}

CTabFolder.MArea .MPartStack, CTabFolder.MArea .MPartStack.active {
	swt-shadow-visible: false;
}

CTabFolder Canvas {
	background-color: #F8F8F8;
}

