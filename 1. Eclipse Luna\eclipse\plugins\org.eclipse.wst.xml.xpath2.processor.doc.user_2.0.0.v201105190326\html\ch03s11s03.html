<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Deep-Equal, Aggregate Functions, and Functions that Generate Sequences</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch03s11.html" title="Functions on Nodes"><link rel="prev" href="ch03s11s02.html" title="Functions That Test the Cardinality of Sequences"><link rel="next" href="ch03s11s04.html" title="Context Functions"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Deep-Equal, Aggregate Functions, and Functions that Generate Sequences"><div class="titlepage"><div><div><h3 class="title"><a name="Deep-Equal.2C_Aggregate_Functions.2C_and_Functions_that_Generate_Sequences"></a>Deep-Equal, Aggregate Functions, and Functions that Generate Sequences</h3></div></div></div><div class="literallayout"><p>avg((3,4,5))<br>
</p></div><p> </p><p>from within a Java application, in order to extract the result from the result sequence, one would have to use this code: </p><p>double avg = ((XSDouble)rs.first()).doublevalue(); println(avg); </p><p>in order to get the result of &lsquo;4.0&rsquo;</p></div></body></html>