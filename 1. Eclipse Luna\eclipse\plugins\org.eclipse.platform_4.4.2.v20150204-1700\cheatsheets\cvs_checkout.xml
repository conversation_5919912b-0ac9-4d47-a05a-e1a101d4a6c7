<?xml version="1.0" encoding="UTF-8" ?> 
<cheatsheet title="Check out a CVS project">

  <intro 
      href="/org.eclipse.platform.doc.user/reference/ref-cheatsheets.htm">
    <description>
      This cheat sheet shows you how to explore a CVS repository and check out
      a project. If you need help at any step, click on the (?) icon to the
      right.
    </description>
  </intro>

  <item
      title="Open the CVS Repository Exploring perspective"
      href="/org.eclipse.platform.doc.user/tasks/tasks-1h.htm">
    <description>
      From the main menu, select Window &gt; Open Perspective &gt; Other...,
      then select the <b>CVS Repository Exploring</b> perspective.
    </description>
    <command
        serialization="org.eclipse.ui.perspectives.showPerspective(org.eclipse.ui.perspectives.showPerspective.perspectiveId=org.eclipse.team.cvs.ui.cvsPerspective)"
        confirm="true" translate="" >
    </command>
  </item>

  <item
      title="Add a CVS repository"
      href="/org.eclipse.platform.doc.user/tasks/tasks-92.htm">
    <description>
      In the CVS Repositories view, click on the <b>Add CVS Repository</b>
      toolbar button. Enter the location and authentication information for
      the repository. For example, for the <b>eclipse</b> repository, enter the
      following:<br/>
      <br/>
      Host: dev.eclipse.org<br/>
      Repository path: /cvsroot/eclipse<br/>
      User: anonymous<br/>
      Connection type: pserver<br/>
      <br/>
      Use defaults for the rest, then click Finish.
    </description>
  </item>

  <item
      title="Locate the project"
      href="/org.eclipse.platform.doc.user/tasks/tasks-1i.htm">
    <description>
      Expand the <b>HEAD</b> node under the newly added repository, and locate
      the project you wish to check out (e.g. to find the source code for the
      cheat sheet view, this is in org.eclipse.ui.cheatsheets)
    </description>
  </item>

  <item
      title="Check out the project"
      href="/org.eclipse.platform.doc.user/tasks/tasks-96.htm">
    <description>
      Right click on the project you wish to check out and select <b>Check
      Out</b>. This will download the latest content from the repository into
      your workspace.
    </description>
  </item>

  <item
      title="View the workspace"
      href="/org.eclipse.platform.doc.isv/guide/resInt_workspace.htm">
    <description>
      To see the project in your workspace, switch to the <b>Resource</b>
      perspective via Window &gt; Open Perspective &gt; Resource to see the
      project in the <b>Project Explorer</b> view. You can now view and work with the
      contents of the project.
    </description>
  </item>

</cheatsheet>
