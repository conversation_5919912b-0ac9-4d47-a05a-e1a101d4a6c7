<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Arithmetic Expressions</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch02s03.html" title="How to use the XPath 2.0 grammar with PsychoPath"><link rel="prev" href="ch02s03s04.html" title="Set difference, intersection and Union"><link rel="next" href="ch02s03s05s02.html" title="Multiplication and Division:"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Arithmetic Expressions"><div class="titlepage"><div><div><h3 class="title"><a name="Arithmetic_Expressions"></a>Arithmetic Expressions</h3></div></div></div><div class="section" title="Unary"><div class="titlepage"><div><div><h4 class="title"><a name="Unary"></a>Unary</h4></div></div></div><p>minus and plus: The unary minus operator changes the sign of a number. For example -1 is minus one, and -1e0 is the double value negative -1. </p></div></div></body></html>