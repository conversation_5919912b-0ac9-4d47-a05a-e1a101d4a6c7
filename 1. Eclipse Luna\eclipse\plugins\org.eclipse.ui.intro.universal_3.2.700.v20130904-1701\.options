# Debugging options for the org.eclipse.ui.intro.universal.

# Master flag for all org.eclipse.ui.intro.universal plugin debug options.
org.eclipse.ui.intro.universal/debug = true

# Enable logging of information messages in the plugin. By default, info
# messages are not logged. Setting this option to true will enable logging
# trace information messages.
org.eclipse.ui.intro.universal/trace/logInfo = true

# Enable logging of performance messages in the plugin. By default, performance
# messages are not logged. Setting this option to true will enable logging
# trace information messages. (note: enabling info logging does not enable
# this flag.)
org.eclipse.ui.intro.universal/trace/logPerformance = false

# Performance flags used by the Performance framework to report failures 
# of specific thresholds.  

# Time to create and display the full Intro view.
# org.eclipse.ui.intro/perf/createView = 1000

# Time needed to switch between Intro standby states.
# org.eclipse.ui.intro/perf/setStandbyState = 300