<!-- ===================================================================== -->
<!-- Custom targets called from a project's generated build.xml            -->
<!-- Set customBuildCallbacks=<path/to/this/file> in your build.properties.-->
<!-- ===================================================================== -->
<project name="Build specific targets and properties" default="noDefault">

	<!-- ===================================================================== -->
	<!-- Default target                                                        -->
	<!-- ===================================================================== -->
	<target name="noDefault">
		<echo message="This file must be called with explicit targets" />
	</target>
	
	<!-- ===================================================================== -->
	<!-- Steps to do before the target gather.bin.parts                        -->
	<!-- Available parameters :                                                -->
	<!--  destination.temp.folder - the directory plugins will be collected to -->
	<!--  feature.directory - the directory containing the resulting feature   -->
	<!-- ===================================================================== -->
	<target name="pre.gather.bin.parts">
	</target>
	
	<!-- ===================================================================== -->
	<!-- Steps to do after the target gather.bin.parts                         -->
	<!-- Available parameters :                                                -->
	<!--  destination.temp.folder - the directory plugins will be collected to -->
	<!--  feature.directory - the directory containing the resulting feature   -->
	<!-- ===================================================================== -->
	<target name="post.gather.bin.parts">
	</target>

</project>
