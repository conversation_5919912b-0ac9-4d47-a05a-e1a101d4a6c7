<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Functions on Nodes</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch03.html" title="How to use XPath 2.0 functions with PsychoPath"><link rel="prev" href="ch03s10.html" title="Functions Related to QNames"><link rel="next" href="ch03s11s02.html" title="Functions That Test the Cardinality of Sequences"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Functions on Nodes"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Functions_on_Nodes"></a>Functions on Nodes</h2></div></div></div><div class="section" title="General Functions on Sequences"><div class="titlepage"><div><div><h3 class="title"><a name="General_Functions_on_Sequences"></a>General Functions on Sequences</h3></div></div></div><div class="literallayout"><p>remove((&lsquo;s&rsquo;,&lsquo;o&rsquo;,&lsquo;m&rsquo;,&lsquo;e&rsquo;,&lsquo;t&rsquo;,&lsquo;h&rsquo;,&lsquo;i&rsquo;,&lsquo;n&rsquo;,&lsquo;g&rsquo;),&nbsp;6)<br>
</p></div><p>from within a Java application, in order to extract the result from the result sequence, one would have to use this code: </p><p>
					for (Iterator iter = rs.iterator(); iter.hasNext();) {</p><div class="literallayout"><p>&nbsp;&nbsp;Object&nbsp;item&nbsp;=&nbsp;iter.next();&nbsp;<br>
&nbsp;&nbsp;String&nbsp;n&nbsp;=&nbsp;((XSString)item).stringvalue();<br>
&nbsp;&nbsp;print(n&nbsp;+&nbsp;"&nbsp;");<br>
</p></div><p>}
					println(""); 
					</p><p>in order to get the result of &lsquo;s o m e t i n g&rsquo;</p></div></div></body></html>