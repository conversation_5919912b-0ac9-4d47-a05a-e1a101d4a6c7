<project name="customTargets.template" default="noDefault">

	<!-- ===================================================================== -->
	<!-- Run a given ${target} on all elements being built -->
	<!-- Add on <ant> task for each top level element being built. -->
	<!-- ===================================================================== -->
	<available property="allElementsFile" file="${builder}/allElements.xml" value="${builder}/allElements.xml"/>
	<property name="allElementsFile" location="${eclipse.pdebuild.templates}/headless-build/allElements.xml"/>

	<import file="${allElementsFile}" />
	<target name="allElements">
		<antcall target="allElementsDelegator" />
	</target>
	
	<!-- ===================================================================== -->
	<!-- ===================================================================== -->
	<target name="getBaseComponents" depends="checkLocalBase" unless="skipBase">
		<get src="${eclipseBaseURL}" dest="${buildDirectory}/../temp-base.zip" />
		<unzip dest="${base}" overwrite="true" src="${buildDirectory}/../temp-base.zip" />
	</target>

	<target name="checkLocalBase">
		<available file="${base}" property="skipBase" />
	</target>

	<!-- ===================================================================== -->
	<!-- Check out map files from correct repository -->
	<!-- Replace values for mapsCheckoutTag as desired. -->
	<!-- ===================================================================== -->
	<target name="getMapFiles" depends="checkLocalMaps" unless="skipMaps">
		<property name="mapsCheckoutTag" value="HEAD" />
		<cvs cvsRoot="${mapsRepo}" package="${mapsRoot}" dest="${buildDirectory}/maps" tag="${mapsCheckoutTag}" />
	</target>

	<target name="checkLocalMaps">
		<available property="skipMaps" file="${buildDirectory}/maps" />
	</target>

	<target name="tagMapFiles" if="tagMaps">
		<cvs dest="${buildDirectory}/maps/${mapsRoot}" command="tag ${mapsTagTag}" />
	</target>

	<!-- ===================================================================== -->

	<target name="clean" unless="noclean">
		<antcall target="allElements">
			<param name="target" value="cleanElement" />
		</antcall>
	</target>

	<target name="gatherLogs">
		<mkdir dir="${buildDirectory}/${buildLabel}/compilelogs" />
		<antcall target="allElements">
			<param name="target" value="gatherLogs" />
		</antcall>
		<unzip dest="${buildDirectory}/${buildLabel}/compilelogs" overwrite="true">
			<fileset dir="${buildDirectory}/features">
				<include name="**/*.log.zip" />
			</fileset>
		</unzip>
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do before setup -->
	<!-- ===================================================================== -->
	<target name="preSetup">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do after setup but before starting the build proper -->
	<!-- ===================================================================== -->
	<target name="postSetup">
		<antcall target="getBaseComponents" />
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do before fetching the build elements -->
	<!-- ===================================================================== -->
	<target name="preFetch">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do after fetching the build elements -->
	<!-- ===================================================================== -->
	<target name="postFetch">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do before the repositories are being processed -->
	<!-- ===================================================================== -->
	<target name="preProcessRepos">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do after the repositories have been processed -->
	<!-- ===================================================================== -->
	<target name="postProcessRepos">
	</target>
	
	<!-- ===================================================================== -->
	<!-- Steps to do before generating the build scripts. -->
	<!-- ===================================================================== -->
	<target name="preGenerate">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do after generating the build scripts. -->
	<!-- ===================================================================== -->
	<target name="postGenerate">
		<antcall target="clean" />
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do before running the build.xmls for the elements being built. -->
	<!-- ===================================================================== -->
	<target name="preProcess">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do after running the build.xmls for the elements being built. -->
	<!-- ===================================================================== -->
	<target name="postProcess">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do before running assemble. -->
	<!-- ===================================================================== -->
	<target name="preAssemble">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do after  running assemble. -->
	<!-- ===================================================================== -->
	<target name="postAssemble">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do before running package. -->
	<!-- ===================================================================== -->
	<target name="prePackage">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do after  running package. -->
	<!-- ===================================================================== -->
	<target name="postPackage">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do after the build is done. -->
	<!-- ===================================================================== -->
	<target name="postBuild">
		<antcall target="gatherLogs" />
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do to test the build results -->
	<!-- ===================================================================== -->
	<target name="test">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do to publish the build results -->
	<!-- ===================================================================== -->
	<target name="publish">
	</target>

	<!-- ===================================================================== -->
	<!-- Default target                                                        -->
	<!-- ===================================================================== -->
	<target name="noDefault">
		<echo message="You must specify a target when invoking this file" />
	</target>

</project>
