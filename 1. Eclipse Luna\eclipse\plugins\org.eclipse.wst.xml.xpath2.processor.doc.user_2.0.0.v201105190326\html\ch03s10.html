<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Functions Related to QNames</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch03.html" title="How to use XPath 2.0 functions with PsychoPath"><link rel="prev" href="ch03s09.html" title="Component Extraction Functions on Durations, Dates and Times"><link rel="next" href="ch03s11.html" title="Functions on Nodes"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Functions Related to QNames"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Functions_Related_to_QNames"></a>Functions Related to QNames</h2></div></div></div><div class="literallayout"><p>local-name-from-QName(QName(&lsquo;http://www.example.com/example&rsquo;,&nbsp;&lsquo;person&rsquo;))<br>
</p></div><p>from within a Java application, in order to extract the result from the result sequence, one would have to use this code: </p><p>String n = ((XSNCName)rs.first()).stringvalue(); println(n); </p><p>in order to get the result of &lsquo;person&rsquo;</p></div></body></html>