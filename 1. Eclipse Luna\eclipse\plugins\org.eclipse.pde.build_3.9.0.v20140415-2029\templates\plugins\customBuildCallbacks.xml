<!-- ===================================================================== -->
<!-- Custom targets called from a project's generated build.xml            -->
<!-- Set customBuildCallbacks=<path/to/this/file> in your build.properties.-->
<!-- ===================================================================== -->
<project name="Build specific targets and properties" default="noDefault">

	<!-- ===================================================================== -->
	<!-- Default target                                                        -->
	<!-- ===================================================================== -->
	<target name="noDefault">
		<echo message="This file must be called with explicit targets" />
	</target>
	
	<!-- ===================================================================== -->
	<!-- Steps to do before the target build.jars                              -->
	<!-- Available parameters :                                                -->
	<!--   build.result.folder - folder to contain the build results           -->
	<!-- ===================================================================== -->
	<target name="pre.build.jars">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do after the target build.jars                               -->
	<!-- Available parameters :                                                -->
	<!--   build.result.folder - folder to contain the build results           -->
	<!-- ===================================================================== -->
	<target name="post.build.jars">
	</target>
	
	<!-- ===================================================================== -->
	<!-- Steps to do before the target build.sources                           -->
	<!-- Available parameters :                                                -->
	<!--   build.result.folder - folder to contain the build results           -->
	<!-- ===================================================================== -->
	<target name="pre.build.sources">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do after the target build.sources                            -->
	<!-- Available parameters :                                                -->
	<!--   build.result.folder - folder to contain the build results           -->
	<!-- ===================================================================== -->
	<target name="post.build.sources">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do before the compilation target <name>                      -->
	<!-- Substitute "name" with the name of the compilation target, eg @dot    -->
	<!-- Available parameters :                                                -->
	<!--   source.foldern : n = 1 ... N, the source folders                    -->
	<!--   target.folder  : where the results of the compilation go            -->
	<!--   <name>.classpath : name = name of the compilation target. A         -->
	<!--                      reference to the classpath structure.            -->
	<!-- ===================================================================== -->
	<target name="pre.name">
	</target>

	<target name="pre.@dot">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do during the compilation target <name>, after the compile   -->
	<!-- but before jaring.  Substitute "name" with the name of the compilation-->
	<!-- target, eg @dot                                                       -->
	<!-- Available parameters :                                                -->
	<!--   source.foldern : n = 1 ... N, the source folders                    -->
	<!--   target.folder  : where the results of the compilation go            -->
	<!--   <name>.classpath : name = name of the compilation target. A         -->
	<!--                      reference to the classpath structure.            -->
	<!-- ===================================================================== -->
	<target name="post.compile.name">
	</target>

	<target name="post.compile.@dot">
	</target>
	
	<!-- ===================================================================== -->
	<!-- Steps to do after the compilation target <name>                       -->
	<!-- Substitute "name" with the name of the compilation target, eg @dot    -->
	<!-- Available parameters :                                                -->
	<!--   jar.Location - the location of the compilation results              -->
	<!--   <name>.classpath : name = name of the compilation target. A         -->
	<!--                      reference to the classpath structure.            -->
	<!-- ===================================================================== -->
	<target name="post.name">
	</target>

	<target name="post.@dot">
	</target>
	
	<!-- ===================================================================== -->
	<!-- Steps to do before the target gather.bin.parts                         -->
	<!-- Available parameters :                                                -->
	<!--   build.result.folder - folder containing the build results           -->
	<!--   target.folder - destination folder                                  -->
	<!-- ===================================================================== -->
	<target name="pre.gather.bin.parts">
	</target>
		
	<!-- ===================================================================== -->
	<!-- Steps to do after the target gather.bin.parts                         -->
	<!-- Available parameters :                                                -->
	<!--   build.result.folder - folder containing the build results           -->
	<!--   target.folder - destination folder                                  -->
	<!-- ===================================================================== -->
	<target name="post.gather.bin.parts">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do before the target gather.sources                          -->
	<!-- Available parameters :                                                -->
	<!--   destination.temp.folder - destination folder                        -->
	<!-- ===================================================================== -->
	<target name="pre.gather.sources">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do after the target gather.sources                           -->
	<!-- Available parameters :                                                -->
	<!--   destination.temp.folder - destination folder                        -->
	<!-- ===================================================================== -->
	<target name="post.gather.sources">
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do before the target gather.logs                             -->
	<!-- Available parameters :                                                -->
	<!--   destination.temp.folder - destination folder                        -->
	<!-- ===================================================================== -->
	<target name="pre.gather.logs">        
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do after the target gather.logs                              -->
	<!-- Available parameters :                                                -->
	<!--   destination.temp.folder - destination folder                        -->
	<!-- ===================================================================== -->
	<target name="post.gather.logs">       
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do before the target clean                                   -->
	<!-- Available parameters :                                                -->
	<!--   destination.temp.folder - destination folder                        -->
	<!-- ===================================================================== -->
	<target name="pre.clean">              
	</target>

	<!-- ===================================================================== -->
	<!-- Steps to do after the target clean                                    -->
	<!-- Available parameters :                                                -->
	<!--   plugin.destination - final destination of the build                 -->
	<!--   build.result.folder - results of the compilation                    -->
	<!--   temp.folder - temporary folder                                      -->
	<!-- ===================================================================== -->
	<target name="post.clean">             
	</target>
</project>
