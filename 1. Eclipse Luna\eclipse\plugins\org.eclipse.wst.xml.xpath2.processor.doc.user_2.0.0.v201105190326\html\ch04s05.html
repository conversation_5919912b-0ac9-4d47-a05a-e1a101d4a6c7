<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Arithmetic Functions on Durations</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch04.html" title="How to use XPath 2.0 operators with PsychoPath"><link rel="prev" href="ch04s04.html" title="Comparisons of Duration, Date and Time Values"><link rel="next" href="ch04s06.html" title="Arithmetic Functions Dates and Times"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Arithmetic Functions on Durations"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Arithmetic_Functions_on_Durations"></a>Arithmetic Functions on Durations</h2></div></div></div><div class="literallayout"><p>multiply-dayTimeDuration(xdt:dayTimeDuration("PT2H10M"),&nbsp;2.1)<br>
</p></div><p> </p><p>from within a Java application, in order to extract the result from the result sequence, one would have to use this code: </p><p>String n = ((XDTDayTimeDuration)rs.first()).stringvalue(); println(n);</p><p>which returns a xdt:dayTimeDuration value corresponding to 4 hours and 33 minutes &lsquo;PT4H33M&rsquo;</p></div></body></html>