<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Range expressions</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch02s03.html" title="How to use the XPath 2.0 grammar with PsychoPath"><link rel="prev" href="ch02s03s05s03.html" title="Addition and Subtraction:"><link rel="next" href="ch02s03s07.html" title="Comparisons"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Range expressions"><div class="titlepage"><div><div><h3 class="title"><a name="Range_expressions"></a>Range expressions</h3></div></div></div><p>The expression E1 to E2 returns a sequence of integers. For example, 1 to 5 returns the sequence 1, 2, 3, 4, 5. This is useful in for expressions, for example the first five nodes of a node sequence can be processed by writing for $i in 1 to 5 return (//x)
					<a class="ulink" href="$i" target="_top">$i</a>. Another example: 
				</p><div class="literallayout"><p>(1+1&nbsp;to&nbsp;10)<br>
</p></div><p> 

					<span class="bold"><strong>result:</strong></span> 
				</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>xs:integer: 2 </p></li><li class="listitem"><p>xs:integer: 3 </p></li><li class="listitem"><p>xs:integer: 4 </p></li><li class="listitem"><p>xs:integer: 5 </p></li><li class="listitem"><p>xs:integer: 6 </p></li><li class="listitem"><p>xs:integer: 7 </p></li><li class="listitem"><p>xs:integer: 8 </p></li><li class="listitem"><p>xs:integer: 9 </p></li><li class="listitem"><p>xs:integer: 10
</p></li></ol></div></div></body></html>