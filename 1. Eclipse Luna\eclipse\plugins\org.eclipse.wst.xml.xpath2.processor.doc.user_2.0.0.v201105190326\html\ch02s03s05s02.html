<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Multiplication and Division:</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch02s03s05.html" title="Arithmetic Expressions"><link rel="prev" href="ch02s03s05.html" title="Arithmetic Expressions"><link rel="next" href="ch02s03s05s03.html" title="Addition and Subtraction:"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Multiplication and Division:"><div class="titlepage"><div><div><h4 class="title"><a name="Multiplication_and_Division:"></a>Multiplication and Division:</h4></div></div></div><p>The operator * multiplies two numbers. If the operands are of different types, XPath 2.0 specifications say that one of them is promoted to the type of the other.&nbsp;<span style="color: red">&lt;strike&gt;&lt;/strike&gt;</span> The result is the same type as the operands after promotion. </p><p>
						 The operator div divides two numbers. Dividing two integers produces a double; in other cases the result is the same type as the operands. </p><p>
						 The operator idiv performs integer division. For example, the result of 10 idiv 3 is 3. </p><p>
						 The mod operator returns the modulus (or remainder) after division. </p><p>
						 The operators * and div may also be used to multiply or divide a range by a number. </p><p>For example, 
						<span class="italic">(1 idiv 1 to 3)</span> returns the result: 
						<span class="italic">xs:integer: 1, xs:integer: 2, xs:integer: 3</span> 
					</p></div></body></html>