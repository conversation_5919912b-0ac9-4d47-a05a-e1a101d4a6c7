<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<title>About</title>
</head>
<body lang="EN-US">
<h2>About This Content</h2>
 
<p>July, 2013</p>	
<h3>License</h3>

<p>The Eclipse Foundation makes available all content in this plug-in (&quot;Content&quot;).  Unless otherwise 
indicated below, the Content is provided to you under the terms and conditions of the
Eclipse Public License Version 1.0 (&quot;EPL&quot;).  A copy of the EPL is available 
at <a href="http://www.eclipse.org/legal/epl-v10.html">http://www.eclipse.org/legal/epl-v10.html</a>.
For purposes of the EPL, &quot;Program&quot; will mean the Content.</p>

<p>If you did not receive this Content directly from the Eclipse Foundation, the Content is 
being redistributed by another party (&quot;Redistributor&quot;) and different terms and conditions may
apply to your use of any object code in the Content.  Check the Redistributor's license that was 
provided with the Content.  If no such license exists, contact the Redistributor.  Unless otherwise
indicated below, the terms and conditions of the EPL still apply to any source code in the Content
and such source code may be obtained at <a href="http://www.eclipse.org">http://www.eclipse.org</a>.</p>

<h3>Third Party Content</h3>

<p>
The Content includes items that have been sourced from third parties as set out below. If you 
did not receive this Content directly from the Eclipse Foundation, the following is provided 
for informational purposes only, and you should look to the Redistributor&rsquo;s license for 
terms and conditions of use.
</p>

<h4>Ant 1.9.2</h4>

<p>
Information about what changed in Ant 1.9.2 from Ant 1.9.1 can be found in the <a href="http://www.apache.org/dist/ant/RELEASE-NOTES-1.9.2.html" alt="Ant 1.9.2 release notes">release notes</a>.
</p>
<p>
The plug-in includes software developed by The Apache Software Foundation as part of the Ant project.
</p>

<p>
The Ant binary code in ant.jar and the scripts ant, ant.bat, ant.cmd, antenv.cmd, antRun, antRun.bat, antRun.pl, complete-ant-cmd.pl, envset.cmd, lcp.bat, runant.pl, runant.py and runrc.cmd are included with the plug-in with no modifications.
</p>

<p>
Your use of the Ant code and the scripts is subject to the terms and conditions of the Apache License, Version 2.0.  A copy of the license is contained
in the file <a href="about_files/ASL-LICENSE-2.0.txt" target="_blank">ASL-LICENSE-2.0.txt</a> and is also available at <a href="http://www.apache.org/licenses/LICENSE-2.0.html" target="_blank">http://www.apache.org/licenses/LICENSE-2.0.html</a>.
</p>
<p>
The names &quot;Ant&quot; and &quot;Apache Software Foundation&quot; must not be used to endorse or promote products derived from this 
software without prior written permission.  For written permission, please contact <a href="mailto:<EMAIL>"><EMAIL></a>.
</p>

<p>
The Apache attribution <a href="about_files/NOTICE" target="_blank">NOTICE</a> file is included with the Content in accordance with 4d of the Apache License, Version 2.0.
</p>

<p>Ant includes the following software:</p>

<blockquote>
	<h4>DOM</h4>
	<p>
	DOM is developed by the World Wide Web Consortium.  Your use of DOM is subject to the terms and conditions of the license found in the
	file <a href="about_files/DOM-LICENSE.html" target="_blank">DOM-LICENSE.html</a> which is included with this plug-in and can also be found at
	<a href="http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231" target="_blank">http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231</a>.
	</p>
	
	<h4>SAX</h4>
	
	<p>SAX is developed by the SAX project (<a href="http://www.saxproject.org" target="_blank">http://www.saxproject.org</a>).  Your use of SAX is subject to the
	terms and conditions of the license found in the file <a href="about_files/SAX-LICENSE.html" target="_blank">SAX-LICENSE.html</a> which is included with this plug-in.</p>
</blockquote>
</body>
</html>