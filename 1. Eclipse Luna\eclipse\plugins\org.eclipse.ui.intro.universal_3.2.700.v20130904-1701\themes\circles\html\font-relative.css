/*******************************************************************************
 * Copyright (c) 2008 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials 
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * 
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *******************************************************************************/

/*
 * Font sizes for the circles theme
 */

/* The label part of the folding section */
.section-title-link .section-title {
	font-size : 100%;
}

h2 {
	font-size : 120%;
}

/* For regular div labels */
H4 .div-label {
	font-size: 120%; 
}

/* For the main page content's title */
#content-header H4 .div-label {
	font-size: 240%; 
}


/* Page description if the page has it. */
.page-description {
	font-size: 100%; 
}

/* General link labels */
a .link-label {
	//font-size : 10pt;
}

/* Floating link labels for navigation links */
#navigation-links a .link-label {
	font-size : 8pt;
}

/* Text in links. */
a .text {
	font-size : 90%;
}

p .group-description {
	font-size : 100%;
}

.content-link .link-label {
	font-size: 120%; 
}

.content-link .text {
	font-size: 100%;
}

.categoryContentnav {
	//font-size:10pt; 
}

.contentpgNavhover {
	font-size: 8pt; 
}

.topicList, .rss-feed-link {
	font-size:90%; 
}

/* Link label properties */
#root #page-links a .link-label {
	font-size : 110%;
}

#root #page-links a:active .link-label,
#root #page-links a:focus .link-label,
#root #page-links a:hover .link-label {
	font-size : 12.5pt;
}

/* Link description properties */
#root #page-links a p .text {
	font-size : 11pt;
}

#root #page-links a:hover .link-extra-div {
	font-size : 13pt;
}

#root .intro-header {
    display : inline;
}

#root .intro-header h1  {
    color : white;
    //padding-top : 10px;
    margin-top : 10px;
    margin-left : 20px;
    font-size : 150%;
}

/*
 * Standby page settings
 */

#standby .intro-header H1 {
	font-size : 150%;
}

#standby #page-links a .link-label, #standby #action-links a .link-label {
	font-size : 100%;
}

#standby #page-links a p .text, #standby #action-links a p .text {
	font-size : 100%;
}