/*******************************************************************************
 * Copyright (c) 2010, 2014 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *     <PERSON> <<PERSON>.<EMAIL>> - Bug 420836
 *******************************************************************************/
@import url("platform:/plugin/org.eclipse.ui.themes/css/e4_basestyle.css");

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_START {
	color: #E6E3C3;
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_END {
	color: #EDEACA;
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_TAB_OUTER_KEYLINE_COLOR {
	color: #BFCDA4;
}

.MTrimmedWindow {
	margin-top: 2px;
	margin-bottom: 2px;
	margin-left: 2px;
	margin-right: 2px;
}

.MTrimmedWindow.topLevel {
	margin-top: 24px;
	margin-bottom: 2px;
	margin-left: 12px;
	margin-right: 12px;
}

.MPart.busy {
	font-style: italic;
}

.MPart.highlighted {
	font-weight: bold;
}

.MPartStack, .MPart {
	font-family: '#org-eclipse-ui-workbench-TAB_TEXT_FONT';
}

.MPartStack {
	swt-simple: true;
	swt-mru-visible: false;
}

.MTrimBar#org-eclipse-ui-main-toolbar {
	background-image:  url(./winXPOlive.png);
}

#PerspectiveSwitcher  {
	background-color: #F5F3ED #F0ECE0 100%;
	eclipse-perspective-keyline-color: #A7B680 #A7B680;
}

.MToolBar.Draggable {
	handle-image:  url(./dragHandle.png);
}

.MToolControl.Draggable {
	handle-image:  url(./dragHandle.png);
}
