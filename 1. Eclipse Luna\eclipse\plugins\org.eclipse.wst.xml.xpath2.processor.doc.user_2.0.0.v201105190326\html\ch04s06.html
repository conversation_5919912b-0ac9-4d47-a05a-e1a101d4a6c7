<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Arithmetic Functions Dates and Times</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch04.html" title="How to use XPath 2.0 operators with PsychoPath"><link rel="prev" href="ch04s05.html" title="Arithmetic Functions on Durations"><link rel="next" href="ch04s07.html" title="Operators Related to QNames And Nodes"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Arithmetic Functions Dates and Times"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Arithmetic_Functions_Dates_and_Times"></a>Arithmetic Functions Dates and Times</h2></div></div></div><div class="literallayout"><p>add-yearMonthDuration-to-dateTime(&nbsp;xs:dateTime("2000-10-30T11:12:00"),&nbsp;xdt:yearMonthDuration("P1Y2M"))<br>
</p></div><p> </p><p>from within a Java application, in order to extract the result from the result sequence, one would have to use this code: </p><p>String n = ((XSDateTime)rs.first()).stringvalue(); println(n);</p><p>which returns an xs:dateTime value corresponding to the lexical representation &lsquo;2001-12-30T11:12:00&rsquo;</p></div></body></html>