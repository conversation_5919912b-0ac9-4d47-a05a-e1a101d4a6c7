<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Comparison of Numeric Values</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch04.html" title="How to use XPath 2.0 operators with PsychoPath"><link rel="prev" href="ch04.html" title="How to use XPath 2.0 operators with PsychoPath"><link rel="next" href="ch04s03.html" title="Operators on Boolean Values"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Comparison of Numeric Values"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Comparison_of_Numeric_Values"></a>Comparison of Numeric Values</h2></div></div></div><div class="literallayout"><p>xs:decimal(3.3)&nbsp;=&nbsp;xs:decimal(6.6)<br>
</p></div><p>from within a Java application, in order to extract the result from the result sequence, one would have to use this code: </p><p>boolean n = ((XSBoolean)rs.first()).value(); println(n);</p><p>in order to get the result of &lsquo;false&rsquo;</p></div></body></html>