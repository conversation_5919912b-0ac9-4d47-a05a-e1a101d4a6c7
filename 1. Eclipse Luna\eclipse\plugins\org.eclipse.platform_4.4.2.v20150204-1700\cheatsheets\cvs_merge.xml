<?xml version="1.0" encoding="UTF-8" ?> 
<cheatsheet title="Merge CVS branches">

  <intro 
      href="/org.eclipse.platform.doc.user/reference/ref-cheatsheets.htm">
    <description>
      This cheat sheet shows you the steps to merge changes from one CVS branch
      into another, or into HEAD. If you need help at any step, click on the (?)
      icon to the right.
    </description>
  </intro>

  <item
      href="/org.eclipse.platform.doc.user/tasks/tasks-100b.htm"
      title="Determine branch and version information">
    <description>
      The first step is to decide <b>which</b> two branches will be merged, at
      which <b>version</b> the content was branched, and what the
      <b>destination</b> branch will be (where the result of the merge will go).
      Once you have this information, you can move on to the next step.
    </description>
  </item>

  <item
      href="/org.eclipse.platform.doc.user/tasks/tasks-96.htm"
      title="Check out the project">
    <description>
      Before merging, you must <b>check out</b> the project you wish to merge
      and bring it into your workspace. You can do this either via the import
      wizard or by adding your repository from the CVS Repository Exploring
      perspective, or click the (?) button for help.
    </description>
  </item>

  <item
      href="/org.eclipse.platform.doc.user/tasks/tasks-103.htm"
      title="Load the destination into your workspace">
    <description>
      In the navigator, right-click on the project and select Replace With &gt;
      Another Branch or Version. Select the destination branch. This will load
      the branch's latest content into your workspace.
    </description>
  </item>

  <item
      href="/org.eclipse.platform.doc.user/tasks/tasks-100b.htm"
      title="Merge details">
    <description>
      Again in the navigator, right-click on the project and select <b>Team</b>
      &gt; <b>Merge</b> and complete the steps in the wizard. In this wizard you
      specify the details of the merge.
    </description>
  </item>

  <item
      href="/org.eclipse.platform.doc.user/tasks/tasks-100b.htm"
      title="Work with the Merge editor">
    <description>
      In the Merge editor manually merge the changes. This step loads the
      required changes into the workspace. Once you've merged all the changes
      and are satisfied with the result, move on to the next step.
    </description>
  </item>

  <item
      href="/org.eclipse.platform.doc.user/tasks/tasks-114.htm"
      title="Commit your changes">
    <description>
      Right-click on the project and select <b>Team</b> &gt; <b>Synchronize with
      Repository</b> and then commit all the changes to the repository. This
      step finalizes the transfer of the changes from the workspace to the CVS
      repository, and finalizes the merge.
    </description>
  </item>

</cheatsheet>
