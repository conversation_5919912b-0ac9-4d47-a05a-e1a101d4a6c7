/*******************************************************************************
 * Copyright (c) 2010, 2014 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *     <PERSON> <<EMAIL>> - Bug 420836
 *******************************************************************************/

/* New ColorDefinitions for the E4 default theme */ 
ThemesExtension { color-definition: 
	'#org-eclipse-ui-workbench-INACTIVE_UNSELECTED_TABS_COLOR_START', 
	'#org-eclipse-ui-workbench-INACTIVE_UNSELECTED_TABS_COLOR_END',
	'#org-eclipse-ui-workbench-INACTIVE_TAB_OUTER_KEYLINE_COLOR',
	'#org-eclipse-ui-workbench-INACTIVE_TAB_INNER_KEYLINE_COLOR',
	'#org-eclipse-ui-workbench-INACTIVE_TAB_OUTLINE_COLOR',
	'#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_START',
	'#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_END',
	'#org-eclipse-ui-workbench-ACTIVE_TAB_OUTER_KEYLINE_COLOR',
	'#org-eclipse-ui-workbench-ACTIVE_TAB_INNER_KEYLINE_COLOR',
	'#org-eclipse-ui-workbench-ACTIVE_TAB_OUTLINE_COLOR';
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_UNSELECTED_TABS_COLOR_START {
	color: #FFFFFF;
	category: '#org-eclipse-ui-presentation-default';
	label: 'Inactive, unselected part color start';
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_UNSELECTED_TABS_COLOR_END {
	color: #FFFFFF;
	category: '#org-eclipse-ui-presentation-default';
	label: 'Inactive, unselected part color end';
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_TAB_OUTER_KEYLINE_COLOR {
	color: #FFFFFF;
	category: '#org-eclipse-ui-presentation-default';
	label: 'Inactive part outer keyline color';
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_TAB_INNER_KEYLINE_COLOR {
	color: #FFFFFF;
	category: '#org-eclipse-ui-presentation-default';
	label: 'Inactive part inner keyline color';
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_TAB_OUTLINE_COLOR {
	color: #B6BCCC;
	category: '#org-eclipse-ui-presentation-default';
	label: 'Inactive part outline color';
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_START {
	color: #FFFFFF;
	category: '#org-eclipse-ui-presentation-default';
	label: 'Active, unselected part color begin';
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_END {
	color: #FFFFFF;
	category: '#org-eclipse-ui-presentation-default';
	label: 'Active, unselected part color end';
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_TAB_OUTER_KEYLINE_COLOR {
	color: #FFFFFF;
	category: '#org-eclipse-ui-presentation-default';
	label: 'Active part outer keyline color';
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_TAB_INNER_KEYLINE_COLOR {
	color: #FFFFFF;
	category: '#org-eclipse-ui-presentation-default';
	label: 'Active part inner keyline color';
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_TAB_OUTLINE_COLOR {
	color: #B6BCCC;
	category: '#org-eclipse-ui-presentation-default';
	label: 'Active part outline color';
}

/* Already existing ColorDefinitions overridden for the E4 default theme */
ColorDefinition#org-eclipse-ui-workbench-INACTIVE_TAB_BG_START {
	color: #dddfe5;
}

ColorDefinition#org-eclipse-ui-workbench-INACTIVE_TAB_BG_END {
	color: #FFFFFF;
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_TAB_BG_START{
	color: #FFFFFF;
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_TAB_BG_END {
	color: #FFFFFF;
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_BG_START {
	color: #FFFFFF;
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_BG_END {
	color: #FFFFFF;
}

ColorDefinition#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_TEXT_COLOR {
	color: #000000;
}

.MTrimmedWindow.topLevel {
	margin-top: 3px;
	margin-bottom: 3px;
	margin-left: 3px;
	margin-right: 3px;
}

.MPart.busy {
	font-style: italic;
}

.MPart.highlighted {
	font-weight: bold;
}

.MPartStack, .MPart {
	font-family: '#org-eclipse-ui-workbench-TAB_TEXT_FONT';
}

CTabItem:selected {
	color: '#org-eclipse-ui-workbench-ACTIVE_TAB_TEXT_COLOR';
}

.MPartStack {
	swt-tab-renderer: url('bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.CTabRendering');
	swt-selected-tab-fill: '#org-eclipse-ui-workbench-INACTIVE_TAB_BG_START' '#org-eclipse-ui-workbench-INACTIVE_TAB_BG_END' 100% 100%;
	swt-unselected-tabs-color: '#org-eclipse-ui-workbench-INACTIVE_UNSELECTED_TABS_COLOR_START' '#org-eclipse-ui-workbench-INACTIVE_UNSELECTED_TABS_COLOR_END' 100% 100%;
	swt-outer-keyline-color: '#org-eclipse-ui-workbench-INACTIVE_TAB_OUTER_KEYLINE_COLOR';
	swt-inner-keyline-color: '#org-eclipse-ui-workbench-INACTIVE_TAB_INNER_KEYLINE_COLOR';
	swt-tab-outline: '#org-eclipse-ui-workbench-INACTIVE_TAB_OUTLINE_COLOR';
	padding: 0px 2px 2px;
	swt-shadow-visible: false;
	swt-mru-visible: false;
	color: '#org-eclipse-ui-workbench-INACTIVE_TAB_TEXT_COLOR';
}

.MPartStack.active {
	swt-selected-tab-fill: '#org-eclipse-ui-workbench-ACTIVE_TAB_BG_START' '#org-eclipse-ui-workbench-ACTIVE_TAB_BG_END' 100% 100%;
	swt-unselected-tabs-color: '#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_START' '#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_END' 100% 100%;
	swt-outer-keyline-color: '#org-eclipse-ui-workbench-ACTIVE_TAB_OUTER_KEYLINE_COLOR';
	swt-inner-keyline-color: '#org-eclipse-ui-workbench-ACTIVE_TAB_INNER_KEYLINE_COLOR';
	swt-tab-outline: '#org-eclipse-ui-workbench-ACTIVE_TAB_OUTLINE_COLOR';
	swt-shadow-visible: false;
}

.MPartStack.active.noFocus {
	swt-selected-tab-fill: '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_BG_START' '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_BG_END' 100% 100%;
}

.MPartStack.active.noFocus > CTabItem:selected {
	color: '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_TEXT_COLOR';
}

#PerspectiveSwitcher {
	eclipse-perspective-keyline-color: #AAB0BF #AAB0BF;
}

.MToolControl.TrimStack {
	frame-image:  url(./winXPTSFrame.png);
	handle-image:  url(./winXPHandle.png);
	frame-cuts: 5px 1px 5px 16px;
}

.MToolBar.Draggable {
	handle-image:  url(./dragHandle.png);
}

.MToolControl.Draggable {
	handle-image:  url(./dragHandle.png);
}

.DragFeedback {
	background-color: COLOR-WIDGET-NORMAL-SHADOW;
}

.ModifiedDragFeedback {
	background-color: #A0A000;
}

.MPartStack > Composite {
	background-color: '#org-eclipse-ui-workbench-INACTIVE_TAB_BG_END';
}

.MPartStack.active > Composite {
	background-color: '#org-eclipse-ui-workbench-ACTIVE_TAB_BG_END';
}

.MPartStack.active.noFocus > Composite {
	background-color: '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_BG_END';
}
