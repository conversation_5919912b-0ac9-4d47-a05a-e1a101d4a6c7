<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Compare and Other Functions on String Values</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch03.html" title="How to use XPath 2.0 functions with PsychoPath"><link rel="prev" href="ch03s04.html" title="Functions to Assemble and Disassemble Strings"><link rel="next" href="ch03s06.html" title="Functions Based on Substring Matching"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Compare and Other Functions on String Values"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Compare_and_Other_Functions_on_String_Values"></a>Compare and Other Functions on String Values</h2></div></div></div><div class="literallayout"><p>concat(&lsquo;un&rsquo;,&nbsp;&lsquo;grateful&rsquo;)<br>
</p></div><p>from within a Java application, in order to extract the result from the result sequence, one would have to use this code: </p><p>String n = ((XSString)rs.first()).stringvalue(); println(n);</p><p>in order to get the result of &lsquo;ungrateful&rsquo;</p></div></body></html>