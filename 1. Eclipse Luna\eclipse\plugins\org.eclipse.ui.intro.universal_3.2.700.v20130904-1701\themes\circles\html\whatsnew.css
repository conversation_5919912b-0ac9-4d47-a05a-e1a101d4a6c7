/*******************************************************************************
 * Copyright (c) 2006 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials 
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * 
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *******************************************************************************/

/* show the "selected" image for this page */
#navigation-links a#whatsnew img, 
#navigation-links a#whatsnew:hover img,
#navigation-links a#whatsnew:focus img,
#navigation-links a#whatsnew:active img {
	background-image : url(../graphics/icons/ctool/wn_nav_64.gif); 
	top : 2px; 
	left : -3px;
}

#navigation-links {
	background-image: url(../graphics/contentpage/wn_banner.jpg);
}

#navigation-links a:hover#whatsnew .link-label,
#navigation-links a:focus#whatsnew .link-label,
#navigation-links a:active#whatsnew .link-label {
	display : none;
}

#navigation-links a:hover#whatsnew,
#navigation-links a:focus#whatsnew,
#navigation-links a:active#whatsnew {
	background-image : none;
}

#navigation-links a:hover#whatsnew .link-extra-div,
#navigation-links a:focus#whatsnew .link-extra-div,
#navigation-links a:active#whatsnew .link-extra-div {
	background-image : none;
}
/*
 * Default images for content links in this page.
 */
.content-link img { background-image : url(../graphics/icons/obj48/new_obj.gif); }
.content-link:hover img { background-image : url(../graphics/icons/obj48/newhov_obj.gif); }