<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Axis steps</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch02s03.html" title="How to use the XPath 2.0 grammar with PsychoPath"><link rel="prev" href="ch02s03s02.html" title="Path expressions"><link rel="next" href="ch02s03s04.html" title="Set difference, intersection and Union"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Axis steps"><div class="titlepage"><div><div><h3 class="title"><a name="Axis_steps"></a>Axis steps</h3></div></div></div><p>The basic primitive for accessing a source document is the axis step. Axis steps may be combined into path expressions using the path operators "/" and "//", and they may be filtered using filter expressions in the same way as the result of any other expression. </p><p>An axis step has the basic form axis::node-test, and selects nodes on a given axis that satisfy the node-test. The axes available are: </p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>element: age </p></li><li class="listitem"><p>element: age</p></li></ol></div><p>The rest of the axes act in the same manner.
					 </p></div></body></html>