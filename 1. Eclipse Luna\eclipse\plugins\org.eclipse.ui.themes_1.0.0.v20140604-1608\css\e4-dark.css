/*******************************************************************************
 * Copyright (c) 2010, 2014 <PERSON> and others.
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *     <PERSON> <<EMAIL>> - initial API and implementation
 *     Lars <PERSON> - initial API and implementation
 *******************************************************************************/

@import url("platform:/plugin/org.eclipse.ui.themes/css/dark/e4-dark_basestyle.css");
@import url("platform:/plugin/org.eclipse.ui.themes/css/dark/e4-dark_globalstyle.css"); /* Remove this to have ONLY the main IDE shell dark */
@import url("platform:/plugin/org.eclipse.ui.themes/css/dark/e4-dark_partstyle.css");


.MTrimmedWindow.topLevel {
    margin-top: 3px;
    margin-bottom: 3px;
    margin-left: 3px;
    margin-right: 3px;
}

.MPartStack, .MPart {
    font-family: '#org-eclipse-ui-workbench-TAB_TEXT_FONT';
}

.MPartStack {
    swt-tab-renderer: url('bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.CTabRendering');
    swt-selected-tab-fill: '#org-eclipse-ui-workbench-INACTIVE_TAB_BG_START' '#org-eclipse-ui-workbench-INACTIVE_TAB_BG_END' 100%; /* title background for selected tab */
    swt-unselected-tabs-color: '#org-eclipse-ui-workbench-INACTIVE_UNSELECTED_TABS_COLOR_START' '#org-eclipse-ui-workbench-INACTIVE_UNSELECTED_TABS_COLOR_END' 100% 100%; /* title background for unselected tab */
    swt-outer-keyline-color: '#org-eclipse-ui-workbench-INACTIVE_TAB_OUTER_KEYLINE_COLOR'; /* border color for whole tabs container */
    swt-inner-keyline-color: '#org-eclipse-ui-workbench-INACTIVE_TAB_INNER_KEYLINE_COLOR';
    swt-tab-outline: '#org-eclipse-ui-workbench-INACTIVE_TAB_OUTLINE_COLOR'; /* border color for selected tab */
    padding: 0px 2px 2px;
    swt-shadow-visible: false;
    swt-mru-visible: true;
    swt-corner-radius: 16px;
}

.MPartStack.active {
    swt-selected-tab-fill: '#org-eclipse-ui-workbench-ACTIVE_TAB_BG_START' '#org-eclipse-ui-workbench-ACTIVE_TAB_BG_END' 100%; /* title background for selected tab */
    swt-unselected-tabs-color: '#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_START' '#org-eclipse-ui-workbench-ACTIVE_UNSELECTED_TABS_COLOR_END' 100% 100%; /* title background for unselected tab */
    swt-outer-keyline-color: '#org-eclipse-ui-workbench-ACTIVE_TAB_OUTER_KEYLINE_COLOR'; /* border color for whole tabs container */
    swt-inner-keyline-color: '#org-eclipse-ui-workbench-ACTIVE_TAB_INNER_KEYLINE_COLOR';
    swt-tab-outline: '#org-eclipse-ui-workbench-ACTIVE_TAB_OUTLINE_COLOR'; /* border color for selected tab */
}

.MPartStack.active.noFocus {
    swt-selected-tabs-fill: '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_BG_START' '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_BG_END' 100% 100%;
}

.MPartStack.empty {
    swt-unselected-tabs-color: '#org-eclipse-ui-workbench-INACTIVE_UNSELECTED_TABS_COLOR_START' #4F5456 #4F5456 99% 100%; /* title background for unselected tab */
    swt-tab-outline: #535354; /* border color for selected tab */
    swt-outer-keyline-color: #515658; /* border color for whole tabs container */
}

.MPart.busy {
    font-style: italic;
}

.MPart.highlighted {
    font-weight: bold;
}

CTabItem,
CTabItem CLabel {
    background-color: '#org-eclipse-ui-workbench-INACTIVE_TAB_BG_END'; /* HACK for background of CTabFolder inner Toolbars */
    color: '#org-eclipse-ui-workbench-INACTIVE_TAB_UNSELECTED_TEXT_COLOR';
}
CTabItem:selected,
CTabItem:selected CLabel {
    color: '#org-eclipse-ui-workbench-INACTIVE_TAB_SELECTED_TEXT_COLOR';
}

.MPartStack.active > CTabItem,
.MPartStack.active > CTabItem CLabel {
    background-color: '#org-eclipse-ui-workbench-ACTIVE_TAB_BG_END'; /* HACK for background of CTabFolder inner Toolbars */
    color: '#org-eclipse-ui-workbench-ACTIVE_TAB_UNSELECTED_TEXT_COLOR';
}
.MPartStack.active > CTabItem:selected,
.MPartStack.active > CTabItem:selected CLabel {
    color: '#org-eclipse-ui-workbench-ACTIVE_TAB_SELECTED_TEXT_COLOR';
}

.MPartStack.active.noFocus > CTabItem:selected {
    color: '#org-eclipse-ui-workbench-ACTIVE_NOFOCUS_TAB_SELECTED_TEXT_COLOR';
}

CTabItem.busy {
    color: #888888;
}

#PerspectiveSwitcher {
    eclipse-perspective-keyline-color: #AAB0BF #AAB0BF;
}

.MToolControl.TrimStack {
    /*frame-image:  url(./gtkTSFrame.png);*/
    handle-image:  url(./dragHandle.png);
    frame-cuts: 5px 1px 5px 16px;
}

.MToolBar.Draggable {
    handle-image:  url(./dragHandle.png);
}

.MToolControl.Draggable {
    handle-image:  url(./dragHandle.png);
}

.DragFeedback {
    background-color: COLOR-WIDGET-NORMAL-SHADOW;
}

.ModifiedDragFeedback {
    background-color: #4176AF;
}

.MTrimmedWindow {
    background-color: #515658;
}

.MTrimBar {
    background-color: #515658;
}

CTabFolder.MArea .MPartStack,CTabFolder.MArea .MPartStack.active {
    swt-shadow-visible: false;
}


CTabFolder Tree, CTabFolder Canvas {
    background-color: #2F2F2F;
    color: #CCC;
}
.MPartStack.active Tree,
.MPartStack.active CTabFolder Canvas {
    background-color: #262626;
    color: #CCC;
}

.MPartStack.active Table {
    background-color: #2F2F2F;
    color: #CCC;
}

.View {
    background-color: #313538;
    color: #F5F5F5;
}


/* ###################### Top Toolbar ########################## */

#org-eclipse-ui-main-toolbar, #PerspectiveSwitcher {
    eclipse-perspective-keyline-color: #585858;
    background-color: #515658 #515658 100%;
    handle-image: none;
    color: #EBE8E4;
}


/* #################### Bottom Status Bar ######################## */

#org-eclipse-ui-StatusLine,
#org-eclipse-ui-ProgressBar,
#org-eclipse-ui-ProgressBar Canvas {
    color: #CCCCCC;
}
#org-eclipse-ui-StatusLine CLabel {
    color: #BDBAB7;
}

StatusLine, ImageBasedFrame{
    color: #BDBAB7;
}
