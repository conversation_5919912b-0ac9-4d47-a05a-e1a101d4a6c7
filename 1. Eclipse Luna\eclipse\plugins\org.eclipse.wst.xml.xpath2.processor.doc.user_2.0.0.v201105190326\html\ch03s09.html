<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Component Extraction Functions on Durations, Dates and Times</title><link href="book.css" type="text/css" rel="stylesheet"><link href="book.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.76.1" name="generator"><link rel="home" href="index.html" title="usermanual"><link rel="up" href="ch03.html" title="How to use XPath 2.0 functions with PsychoPath"><link rel="prev" href="ch03s08.html" title="Functions on Boolean Values"><link rel="next" href="ch03s10.html" title="Functions Related to QNames"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="section" title="Component Extraction Functions on Durations, Dates and Times"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Component_Extraction_Functions_on_Durations.2C_Dates_and_Times"></a>Component Extraction Functions on Durations, Dates and Times</h2></div></div></div><div class="literallayout"><p>timezone-from-time(xs:time("13:20:00+05:00"))<br>
</p></div><p>from within a Java application, in order to extract the result from the result sequence, one would have to use this code: </p><p>String n = ((XDTDayTimeDuration)rs.first()).stringvalue(); println(n);</p><p>in order to get the result of &lsquo;PT5H&rsquo;</p></div></body></html>